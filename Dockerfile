# Multi-stage Docker build for Go web2-backend
# Stage 1: Build stage using official Go image with required version
FROM --platform=$BUILDPLATFORM widnyana/go-builder:1.24-alpine AS builder

# Install additional build dependencies
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build the application with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=${TARGETARCH} go build \
    -ldflags="-w -s -X main.version=${BUILD_VERSION} -X main.buildDate=${BUILD_DATE} -X main.commitHash=${VCS_REF}" \
    -a -installsuffix cgo \
    -o /app/http-server \
    ./main.go

# Stage 2: Production stage using minimal RHEL UBI
FROM --platform=$BUILDPLATFORM registry.access.redhat.com/ubi9/ubi-minimal:latest AS production

# Build arguments
ARG BUILD_DATE
ARG BUILD_VERSION
ARG VCS_REF

# Metadata labels
LABEL name="telescope-backend" \
    version="${BUILD_VERSION}" \
    build-date="${BUILD_DATE}" \
    vcs-ref="${VCS_REF}" \
    description="Telescope Web2 Backend - Cross-Border Payroll API" \
    maintainer="LumineLabs" \
    org.opencontainers.image.title="telescope-backend" \
    org.opencontainers.image.description="Telescope Web2 Backend - Cross-Border Payroll API" \
    org.opencontainers.image.version="${BUILD_VERSION}" \
    org.opencontainers.image.created="${BUILD_DATE}" \
    org.opencontainers.image.revision="${VCS_REF}" \
    org.opencontainers.image.source="https://github.com/luminelabs/telescope-be"

# Install minimal runtime dependencies
RUN microdnf update -y && \
    microdnf install -y ca-certificates tzdata && \
    microdnf clean all

# Create non-root user for security
RUN groupadd -r telescope && useradd -r -g telescope telescope

# Set working directory
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder --chown=telescope:telescope /app/http-server /app/http-server

# Copy any necessary config files (if they exist)
COPY --from=builder --chown=telescope:telescope /app/migrations ./migrations

# Switch to non-root user
USER telescope

# Expose port (default from CLAUDE.md is 8888)
EXPOSE 8888

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8888/health || exit 1

# Set environment variables
ENV TELESCOPE_HTTP_PORT=8888

# Run the application
CMD ["/app/http-server"]
