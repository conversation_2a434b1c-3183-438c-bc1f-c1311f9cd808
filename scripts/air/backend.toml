root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
args_bin = ["server"]
bin = "./bin/telescope"
cmd = "go build -race -o ./bin/telescope ."
delay = 1000
exclude_dir = [
  ".bin",
  ".vscode",
  "assets",
  "datasource",
  "docs",
  "documentation",
  "testdata",
  "tmp",
  "etc",
  "vendor",
  "migrations",
  "target",
  "reports",
  "scripts",
  "bin",
  "pkg",
  "configs",
  "deployment",
]
exclude_file = [
  ".mise.toml",
  ".tool-versions",
  "README.md",
  "Makefile",
  ".env.example",
  ".dockerignore",
  "go.mod",
  "go.sum",
]
exclude_regex = ["_test.go", ".*\\.yaml$", ".*\\.yml$", ".*\\.md$", ".*\\.sql$"]
exclude_unchanged = false
follow_symlink = false
full_bin = ""
include_dir = []
include_ext = ["go", "tpl", "tmpl", "html"]
include_file = []
kill_delay = "0s"
log = "telescope.build-errors.log"
poll = true
poll_interval = 0
post_cmd = []
pre_cmd = []
rerun = false
rerun_delay = 500
send_interrupt = true
stop_on_error = false

[color]
app = ""
build = "yellow"
main = "magenta"
runner = "green"
watcher = "cyan"

[log]
main_only = false
silent = false
time = true

[misc]
clean_on_exit = false

[proxy]
app_port = 0
enabled = false
proxy_port = 0

[screen]
clear_on_rebuild = true
keep_scroll = true
