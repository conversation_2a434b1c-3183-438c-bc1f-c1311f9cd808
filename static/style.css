/* API Documentation Styles */

/* Reset and base styles */
* {
	box-sizing: border-box;
}

body {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
	line-height: 1.6;
	color: #e4e4e7;
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
	background-color: #18181b;
	min-height: 100vh;
}

/* Header styles */
header {
	border-bottom: 1px solid #3f3f46;
	margin-bottom: 2rem;
	padding-bottom: 1rem;
}

header h1 {
	margin: 0 0 0.5rem 0;
	color: #f4f4f5;
	font-size: 2rem;
	font-weight: 600;
}

header p {
	color: #a1a1aa;
	margin: 0;
	font-size: 1.1rem;
}

/* Navigation styles */
.doc-nav {
	margin-bottom: 2rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid #3f3f46;
}

.back-link {
	color: #a78bfa;
	text-decoration: none;
	font-size: 0.9rem;
	font-weight: 500;
}

.back-link:hover {
	text-decoration: underline;
	color: #c4b5fd;
}

/* Main content styles */
main {
	margin-bottom: 2rem;
}

.doc-content {
	max-width: none;
}

/* Index page styles */
.doc-index {
	list-style: none;
	padding: 0;
	margin: 0;
}

.doc-index li {
	margin-bottom: 0.5rem;
	padding: 0.75rem 1rem;
	border: 1px solid #3f3f46;
	border-radius: 6px;
	background-color: #27272a;
	transition: background-color 0.2s;
}

.doc-index li:hover {
	background-color: #3f3f46;
}

.doc-index a {
	color: #a78bfa;
	text-decoration: none;
	font-weight: 500;
	display: block;
}

.doc-index a:hover {
	text-decoration: underline;
	color: #c4b5fd;
}

/* Markdown content styles */
.markdown-body {
	font-size: 16px;
}

.markdown-body a {
	color: #a78bfa;
	text-decoration: none;
}

.markdown-body a:hover {
	color: #c4b5fd;
	text-decoration: underline;
}

.markdown-body a:visited {
	color: #9333ea;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
	margin-top: 2rem;
	margin-bottom: 1rem;
	font-weight: 600;
	line-height: 1.25;
	color: #f4f4f5;
}

.markdown-body h1 {
	font-size: 2rem;
	border-bottom: 1px solid #3f3f46;
	padding-bottom: 0.5rem;
}

.markdown-body h2 {
	font-size: 1.5rem;
	border-bottom: 1px solid #3f3f46;
	padding-bottom: 0.5rem;
}

.markdown-body h3 {
	font-size: 1.25rem;
}

.markdown-body p {
	margin-bottom: 1rem;
}

.markdown-body ul,
.markdown-body ol {
	margin-bottom: 1rem;
	padding-left: 2rem;
}

.markdown-body li {
	margin-bottom: 0.25rem;
}

.markdown-body blockquote {
	padding: 0 1rem;
	color: #a1a1aa;
	border-left: 0.25rem solid #52525b;
	margin-bottom: 1rem;
}

.markdown-body table {
	border-collapse: collapse;
	margin-bottom: 1rem;
	width: 100%;
}

.markdown-body th,
.markdown-body td {
	padding: 6px 13px;
	border: 1px solid #3f3f46;
}

.markdown-body th {
	font-weight: 600;
	background-color: #27272a;
}

/* Code styles */
.markdown-body code {
	padding: 0.2em 0.4em;
	margin: 0;
	font-size: 85%;
	background-color: #27272a;
	border-radius: 3px;
	font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
	color: #f87171;
}

.markdown-body pre {
	padding: 16px;
	overflow: auto;
	font-size: 85%;
	line-height: 1.45;
	background-color: #1e1e20;
	border-radius: 6px;
	margin-bottom: 1rem;
	border: 1px solid #3f3f46;
}

.markdown-body pre code {
	background-color: transparent;
	border: 0;
	display: inline;
	line-height: inherit;
	margin: 0;
	max-width: auto;
	overflow: visible;
	padding: 0;
	word-wrap: normal;
}

/* Syntax highlighting styles */
.highlight {
	background-color: #1e1e20;
	border-radius: 6px;
	padding: 16px;
	margin-bottom: 1rem;
	overflow-x: auto;
	border: 1px solid #3f3f46;
}

.highlight pre {
	background-color: transparent;
	padding: 0;
	margin: 0;
	border-radius: 0;
}

.highlight .line-numbers {
	color: #71717a;
	margin-right: 16px;
	user-select: none;
}

/* Chroma syntax highlighting classes - Catppuccin Mocha theme */
.chroma .k { color: #cba6f7; } /* Keyword */
.chroma .s { color: #a6e3a1; } /* String */
.chroma .c { color: #6c7086; } /* Comment */
.chroma .n { color: #cdd6f4; } /* Name */
.chroma .o { color: #89b4fa; } /* Operator */
.chroma .p { color: #cdd6f4; } /* Punctuation */
.chroma .m { color: #fab387; } /* Number */
.chroma .err { color: #f38ba8; } /* Error */
.chroma .kn { color: #f5c2e7; } /* Keyword.Namespace */
.chroma .kc { color: #fab387; } /* Keyword.Constant */
.chroma .kt { color: #f9e2af; } /* Keyword.Type */
.chroma .nf { color: #89b4fa; } /* Name.Function */
.chroma .nc { color: #f9e2af; } /* Name.Class */
.chroma .sb { color: #a6e3a1; } /* String.Backtick */
.chroma .sc { color: #a6e3a1; } /* String.Char */
.chroma .sd { color: #a6e3a1; } /* String.Doc */
.chroma .s2 { color: #a6e3a1; } /* String.Double */
.chroma .se { color: #f5c2e7; } /* String.Escape */
.chroma .si { color: #f5c2e7; } /* String.Interpol */

/* Footer styles */
.doc-footer {
	margin-top: 3rem;
	padding-top: 1rem;
	border-top: 1px solid #3f3f46;
	text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
	body {
		padding: 10px;
	}

	header h1 {
		font-size: 1.5rem;
	}

	.markdown-body h1 {
		font-size: 1.5rem;
	}

	.markdown-body h2 {
		font-size: 1.25rem;
	}

	.doc-index li {
		padding: 0.5rem;
	}
}

/* Print styles */
@media print {
	.doc-nav,
	.doc-footer {
		display: none;
	}

	body {
		max-width: none;
		padding: 0;
	}
}
