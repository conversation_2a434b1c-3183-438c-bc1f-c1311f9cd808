// Package helper provides utility functions for JWT operations and UUID generation.
package helper

import (
	"errors"
	"fmt"
	"time"

	jwt "github.com/golang-jwt/jwt/v5"

	"telescope-be/internal/appctx"
)

type JWTClaims struct {
	UserID string `json:"user_id"`
	Email  string `json:"email"`
	jwt.RegisteredClaims
}

type JWTHelper struct {
	secretKey            []byte
	accessTokenDuration  time.Duration
	refreshTokenDuration time.Duration
}

func NewJWTHelper(secretKey string, accessTokenDuration time.Duration, refreshTokenDuration time.Duration) *JWTHelper {
	return &JWTHelper{
		secretKey:            []byte(secretKey),
		accessTokenDuration:  accessTokenDuration,
		refreshTokenDuration: refreshTokenDuration,
	}
}

func (j *JWTHelper) GenerateAccessToken(userID string, email string) (string, error) {
	claims := &JWTClaims{
		UserID: userID,
		Email:  email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.accessTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

func (j *JWTHelper) GenerateRefreshToken(userID string, _ string) (string, error) {
	claims := &jwt.RegisteredClaims{
		Subject:   userID,
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.refreshTokenDuration)),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		NotBefore: jwt.NewNumericDate(time.Now()),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

func (j *JWTHelper) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// NewJWTHelperFromConfig creates a JWT helper using configuration
func NewJWTHelperFromConfig(config *appctx.Config) (*JWTHelper, error) {
	accessDuration, err := time.ParseDuration(config.JWT.AccessTokenDuration)
	if err != nil {
		return nil, fmt.Errorf("invalid access token duration: %w", err)
	}

	refreshDuration, err := time.ParseDuration(config.JWT.RefreshTokenDuration)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token duration: %w", err)
	}

	return NewJWTHelper(config.JWT.SecretKey, accessDuration, refreshDuration), nil
}

// GetSecretKey returns the secret key for token signing
func (j *JWTHelper) GetSecretKey() []byte {
	return j.secretKey
}
