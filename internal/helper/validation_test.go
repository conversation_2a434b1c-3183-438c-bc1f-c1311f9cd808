package helper

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsValidUsername(t *testing.T) {
	tests := []struct {
		name     string
		username string
		expected bool
	}{
		// Valid usernames
		{"valid simple username", "user123", true},
		{"valid with underscore", "user_123", true},
		{"valid with hyphen", "user-123", true},
		{"valid with dot", "user.123", true},
		{"valid mixed", "user_123.test-name", true},
		{"minimum length", "abc", true},
		{"alphanumeric only", "user123test", true},

		// Invalid usernames
		{"too short", "ab", false},
		{"empty string", "", false},
		{"starts with underscore", "_user", false},
		{"starts with hyphen", "-user", false},
		{"starts with dot", ".user", false},
		{"ends with underscore", "user_", false},
		{"ends with hyphen", "user-", false},
		{"ends with dot", "user.", false},
		{"consecutive underscores", "user__test", false},
		{"consecutive hyphens", "user--test", false},
		{"consecutive dots", "user..test", false},
		{"consecutive mixed", "user._test", false},
		{"contains space", "user test", false},
		{"contains special chars", "user@test", false},
		{"too long", string(make([]byte, 256)), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidUsername(tt.username)
			assert.Equal(t, tt.expected, result, "Username: %s", tt.username)
		})
	}
}

func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		// Valid emails
		{"simple email", "<EMAIL>", true},
		{"email with subdomain", "<EMAIL>", true},
		{"email with numbers", "<EMAIL>", true},
		{"email with dots", "<EMAIL>", true},
		{"email with plus", "<EMAIL>", true},
		{"email with hyphen", "<EMAIL>", true},

		// Invalid emails
		{"empty string", "", false},
		{"no @ symbol", "userexample.com", false},
		{"no domain", "user@", false},
		{"no local part", "@example.com", false},
		{"multiple @ symbols", "user@@example.com", false},
		{"no TLD", "user@example", false},
		{"invalid TLD", "user@example.c", false},
		{"too long", string(make([]byte, 255)) + "@example.com", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidEmail(tt.email)
			assert.Equal(t, tt.expected, result, "Email: %s", tt.email)
		})
	}
}

func TestIsEmailOrUsername(t *testing.T) {
	tests := []struct {
		name       string
		identifier string
		expected   string
	}{
		// Valid emails
		{"valid email", "<EMAIL>", "email"},
		{"email with subdomain", "<EMAIL>", "email"},

		// Valid usernames
		{"valid username", "user123", "username"},
		{"username with underscore", "user_123", "username"},
		{"username with hyphen", "user-123", "username"},

		// Invalid identifiers
		{"empty string", "", "invalid"},
		{"whitespace only", "   ", "invalid"},
		{"invalid format", "user@", "invalid"},
		{"too short username", "ab", "invalid"},
		{"invalid username chars", "user@test", "invalid"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsEmailOrUsername(tt.identifier)
			assert.Equal(t, tt.expected, result, "Identifier: %s", tt.identifier)
		})
	}
}

func TestSanitizeIdentifier(t *testing.T) {
	tests := []struct {
		name       string
		identifier string
		expected   string
	}{
		{"trim whitespace", "  <EMAIL>  ", "<EMAIL>"},
		{"convert to lowercase", "<EMAIL>", "<EMAIL>"},
		{"mixed case username", "UserName123", "username123"},
		{"already clean", "<EMAIL>", "<EMAIL>"},
		{"empty string", "", ""},
		{"whitespace only", "   ", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeIdentifier(tt.identifier)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidateLoginIdentifier(t *testing.T) {
	tests := []struct {
		name           string
		identifier     string
		expectedType   string
		expectedError  bool
		errorMessage   string
	}{
		// Valid cases
		{"valid email", "<EMAIL>", "email", false, ""},
		{"valid username", "user123", "username", false, ""},

		// Invalid cases
		{"empty identifier", "", "", true, "identifier is required"},
		{"invalid format", "user@", "", true, "identifier must be a valid email address or username"},
		{"too short username", "ab", "", true, "identifier must be a valid email address or username"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			identifierType, err := ValidateLoginIdentifier(tt.identifier)

			if tt.expectedError {
				assert.Error(t, err)
				assert.True(t, IsValidationError(err))
				assert.Contains(t, err.Error(), tt.errorMessage)
				assert.Empty(t, identifierType)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedType, identifierType)
			}
		})
	}
}

func TestValidationError(t *testing.T) {
	err := NewValidationError("test error")
	assert.Equal(t, "test error", err.Error())
	assert.True(t, IsValidationError(err))

	// Test with non-validation error
	regularErr := assert.AnError
	assert.False(t, IsValidationError(regularErr))
}

func TestHasConsecutiveSpecialChars(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{"no consecutive", "user_test", false},
		{"consecutive underscores", "user__test", true},
		{"consecutive dots", "user..test", true},
		{"consecutive hyphens", "user--test", true},
		{"consecutive mixed", "user._test", true},
		{"single special char", "user_", false},
		{"no special chars", "usertest", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := hasConsecutiveSpecialChars(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
