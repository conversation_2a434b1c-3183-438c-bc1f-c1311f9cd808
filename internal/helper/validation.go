// Package helper provides utility functions for validation and input sanitization.
package helper

import (
	"sync"

	"telescope-be/internal/validation"

	"go.uber.org/zap"
)

const (
	// Username validation constraints
	MinUsernameLength = 3
	MaxUsernameLength = 255
)

var (
	// validatorInstance holds a singleton instance of the validator service
	validatorInstance validation.Validator
	validatorOnce     sync.Once
)

// getValidator returns a singleton instance of the validator service
func getValidator() validation.Validator {
	validatorOnce.Do(func() {
		// Create a logger for the validator (using a simple logger for now)
		logger, _ := zap.NewProduction()
		validatorInstance = validation.NewValidator(logger)
	})
	return validatorInstance
}

// IsValidUsername validates if a string is a valid username
// Rules:
// - Length between 3-255 characters
// - Must start and end with alphanumeric characters
// - Can contain alphanumeric, underscore, hyphen, and dot
// - Cannot have consecutive special characters
func IsValidUsername(username string) bool {
	return getValidator().IsValidUsername(username)
}

// IsValidEmail validates if a string is a valid email address
// This provides basic validation, but should be used alongside
// go-playground/validator for comprehensive validation
func IsValidEmail(email string) bool {
	return getValidator().IsValidEmail(email)
}

// IsEmailOrUsername determines if an identifier is an email or username
// Returns "email", "username", or "invalid"
func IsEmailOrUsername(identifier string) string {
	return getValidator().IsEmailOrUsername(identifier)
}

// SanitizeIdentifier cleans and normalizes an identifier
// Trims whitespace and converts to lowercase for consistent lookup
func SanitizeIdentifier(identifier string) string {
	return getValidator().SanitizeIdentifier(identifier)
}

// ValidateLoginIdentifier validates that an identifier is either a valid email or username
// Returns the type ("email" or "username") and any validation error
func ValidateLoginIdentifier(identifier string) (string, error) {
	if identifier == "" {
		return "", NewValidationError("identifier is required")
	}

	// Use the validator service for detailed error messages
	validator := getValidator()
	if validationErr := validator.FieldWithDetails(identifier, "email_or_username", "identifier"); validationErr != nil {
		return "", NewValidationError(validationErr.Message)
	}

	identifierType := validator.IsEmailOrUsername(identifier)
	return identifierType, nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// NewValidationError creates a new validation error
func NewValidationError(message string) *ValidationError {
	return &ValidationError{Message: message}
}

// IsValidationError checks if an error is a validation error
func IsValidationError(err error) bool {
	_, ok := err.(*ValidationError)
	return ok
}
