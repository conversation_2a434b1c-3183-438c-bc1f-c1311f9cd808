// Package helper provides utility functions for validation and input sanitization.
package helper

import (
	"regexp"
	"strings"
)

const (
	// Username validation constraints
	MinUsernameLength = 3
	MaxUsernameLength = 255
)

var (
	// usernameRegex defines the allowed pattern for usernames
	// Allows alphanumeric characters, underscores, hyphens, and dots
	// Must start and end with alphanumeric characters
	// Cannot have consecutive special characters
	usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$`)
	
	// emailRegex provides basic email validation
	// This is a simplified regex for basic validation
	// More comprehensive validation should use go-playground/validator
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
)

// IsValidUsername validates if a string is a valid username
// Rules:
// - Length between 3-255 characters
// - Must start and end with alphanumeric characters
// - Can contain alphanumeric, underscore, hyphen, and dot
// - Cannot have consecutive special characters
func IsValidUsername(username string) bool {
	if len(username) < MinUsernameLength || len(username) > MaxUsernameLength {
		return false
	}
	
	// Check if username matches the allowed pattern
	if !usernameRegex.MatchString(username) {
		return false
	}
	
	// Additional check: no consecutive special characters
	return !hasConsecutiveSpecialChars(username)
}

// IsValidEmail validates if a string is a valid email address
// This provides basic validation, but should be used alongside
// go-playground/validator for comprehensive validation
func IsValidEmail(email string) bool {
	if len(email) == 0 || len(email) > 254 { // RFC 5321 limit
		return false
	}
	
	return emailRegex.MatchString(email)
}

// IsEmailOrUsername determines if an identifier is an email or username
// Returns "email", "username", or "invalid"
func IsEmailOrUsername(identifier string) string {
	identifier = strings.TrimSpace(identifier)
	
	if identifier == "" {
		return "invalid"
	}
	
	// Check if it's a valid email first
	if IsValidEmail(identifier) {
		return "email"
	}
	
	// Check if it's a valid username
	if IsValidUsername(identifier) {
		return "username"
	}
	
	return "invalid"
}

// SanitizeIdentifier cleans and normalizes an identifier
// Trims whitespace and converts to lowercase for consistent lookup
func SanitizeIdentifier(identifier string) string {
	return strings.ToLower(strings.TrimSpace(identifier))
}

// hasConsecutiveSpecialChars checks if string has consecutive special characters
func hasConsecutiveSpecialChars(s string) bool {
	specialChars := "._-"
	for i := 0; i < len(s)-1; i++ {
		if strings.ContainsRune(specialChars, rune(s[i])) && 
		   strings.ContainsRune(specialChars, rune(s[i+1])) {
			return true
		}
	}
	return false
}

// ValidateLoginIdentifier validates that an identifier is either a valid email or username
// Returns the type ("email" or "username") and any validation error
func ValidateLoginIdentifier(identifier string) (string, error) {
	if identifier == "" {
		return "", NewValidationError("identifier is required")
	}
	
	identifierType := IsEmailOrUsername(identifier)
	if identifierType == "invalid" {
		return "", NewValidationError("identifier must be a valid email address or username")
	}
	
	return identifierType, nil
}

// ValidationError represents a validation error
type ValidationError struct {
	Message string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// NewValidationError creates a new validation error
func NewValidationError(message string) *ValidationError {
	return &ValidationError{Message: message}
}

// IsValidationError checks if an error is a validation error
func IsValidationError(err error) bool {
	_, ok := err.(*ValidationError)
	return ok
}
