package helper

import (
	"strings"

	"github.com/google/uuid"
)

// GenerateUUIDv7 generates a new UUID v7
func GenerateUUIDv7() (uuid.UUID, error) {
	_id, err := uuid.NewV7()
	if err != nil {
		return _id, err
	}

	return _id, nil
}

// GenerateUUID generates a new UUID v7 string
func GenerateUUID() (string, error) {
	id, err := GenerateUUIDv7()
	if err != nil {
		return "", err
	}
	return id.String(), nil
}

// GenerateUUIDWithoutHyphens generates a UUID string without hyphens
func GenerateUUIDWithoutHyphens() (string, error) {
	id, err := uuid.NewV7()
	if err != nil {
		return "", err
	}
	return strings.ReplaceAll(id.String(), "-", ""), nil
}
