// Package service provides business logic service interfaces and contracts.
package service

import (
	"telescope-be/internal/service/duck"
	"telescope-be/internal/service/resettoken"
	"telescope-be/internal/service/user"
)

type Service struct {
	DuckService       duck.DuckService
	UserService       user.UserService
	ResetTokenService resettoken.Service
}

// NewService creates a new service instance with injected dependencies
func NewService(duckService duck.DuckService, userService user.UserService, resetTokenService resettoken.Service) *Service {
	return &Service{
		DuckService:       duckService,
		UserService:       userService,
		ResetTokenService: resetTokenService,
	}
}
