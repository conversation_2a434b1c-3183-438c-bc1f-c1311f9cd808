package resettoken

import (
	"context"
	"errors"
	"telescope-be/internal/entity"
	"telescope-be/internal/helper"
	"telescope-be/internal/presentation"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// VerifyResetToken checks if the provided reset token is valid and marks it as used if so.
// It retrieves the reset token by ID, checks the token validity, and updates its status.
//
// Parameters:
//   - ctx: The context for managing request-scoped values, deadlines, and cancellation signals.
//   - req: VerifyResetTokenRequest containing the reset token ID and token string.
//
// Returns:
//   - A pointer to the ResetToken if valid and successfully marked as used.
//   - An error if the operation fails or if the token is invalid or already used.
func (s *service) VerifyResetToken(
	ctx context.Context,
	req presentation.VerifyResetTokenRequest,
) (*presentation.ResetTokenResponse, error) {
	var (
		eventName = "resettoken.service.VerifyResetToken"
		lf        = []zapcore.Field{
			zap.String("event_name", eventName),
			zap.String("req.reset_id", req.ResetID),
			zap.String("req.token", req.Token),
		}
		sa = []attribute.KeyValue{
			attribute.String("reset_id", req.ResetID),
			attribute.String("token", req.Token),
		}
	)
	ctx, span := otel.Tracer("resettoken.service").Start(ctx, eventName)
	defer span.End()
	span.SetAttributes(sa...)

	resetToken, err := s.repo.ResetToken.GetResetTokenByID(ctx, req.ResetID)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to get reset token by ID", append(lf, zap.Error(err))...)
		return nil, err
	}

	if !s.tokenIsValid(resetToken, req.Token) {
		err := errors.New("reset token is invalid")
		span.RecordError(err)
		s.logger.Error("invalid or expired reset token", append(lf, zap.Error(err))...)
		return nil, err
	}

	tNow := time.Now()
	resetToken.UsedAt = &tNow

	err = s.updateUserPassword(ctx, resetToken.UserID, req.Password)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to update user password", append(lf, zap.Error(err))...)
		return nil, err
	}

	s.logger.Info("reset token verified successfully", append(lf, zap.Any("user_id", resetToken.UserID))...)

	return &presentation.ResetTokenResponse{
		ResetID:   resetToken.ExternalID,
		Token:     resetToken.Token,
		CreatedAt: resetToken.CreatedAt,
		ExpiresAt: resetToken.ExpiresAt,
		UsedAt:    resetToken.UsedAt,
	}, nil
}

func (s *service) tokenIsValid(resetToken *entity.ResetToken, token string) bool {
	if resetToken.Token != token {
		return false
	}
	if resetToken.UsedAt != nil {
		return false
	}
	if time.Now().After(resetToken.ExpiresAt) {
		return false
	}
	return true
}

func (s *service) updateUserPassword(ctx context.Context, userID int, newPassword string) error {
	var (
		eventName = "resettoken.service.updateUserPassword"
	)
	ctx, span := otel.Tracer("resettoken.service").Start(ctx, eventName)
	defer span.End()

	hashedPassword, err := helper.HashPassword(newPassword)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to hash new password", zap.Error(err))
		return err
	}

	err = s.repo.User.UpdatePassword(ctx, entity.User{
		ID:           userID,
		PasswordHash: hashedPassword,
	})
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to update user password in repository", zap.Int("user_id", userID), zap.Error(err))
		return err
	}

	return nil
}
