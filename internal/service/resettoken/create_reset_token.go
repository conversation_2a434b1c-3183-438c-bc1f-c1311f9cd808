package resettoken

import (
	"context"
	"fmt"
	"telescope-be/internal/entity"
	"telescope-be/internal/presentation"
	"time"

	"github.com/google/go-querystring/query"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// CreateResetToken generates a reset token for a user and stores it in the database
// to allow password reset functionality.
//
// Parameters:
//   - ctx: The context for managing request-scoped values, deadlines, and cancellation signals.
//   - req: CreateResetTokenRequest containing the user's email address.
//
// Returns:
//   - A pointer to the created ResetToken object if successful.
//   - An error if the operation fails or if the user does not exist.
func (s *service) CreateResetToken(
	ctx context.Context,
	req presentation.CreateResetTokenRequest,
) error {
	var (
		eventName = "resettoken.service.CreateResetToken"
		lf        = []zapcore.Field{
			zap.String("event_name", eventName),
			zap.String("req.email", req.Email),
		}
		sa = []attribute.KeyValue{
			attribute.String("email", req.Email),
		}
	)
	ctx, span := otel.Tracer("resettoken.service").Start(ctx, eventName)
	defer span.End()
	span.SetAttributes(sa...)

	usr, err := s.repo.User.GetUserByEmail(ctx, req.Email)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to get user by email", append(lf, zap.Error(err))...)
		return err
	}
	if usr == nil {
		return nil // We should not expose user existence, just return empty token
	}

	tNow := time.Now()
	resetToken := entity.ResetToken{
		ExternalID: uuid.New(),
		UserID:     usr.ID,
		Token:      uuid.New().String(),
		CreatedAt:  tNow,
		ExpiresAt:  tNow.Add(time.Hour),
		UsedAt:     nil,
	}

	err = s.repo.ResetToken.CreateResetToken(ctx, resetToken)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to create reset token", append(lf, zap.Error(err))...)
		return err
	}

	s.logger.Info("reset token created successfully", append(lf, zap.Int("user_id", usr.ID))...)

	emailPayload := presentation.ResetTokenResponse{
		ResetID:   resetToken.ExternalID,
		Token:     resetToken.Token,
		CreatedAt: resetToken.CreatedAt,
		ExpiresAt: resetToken.ExpiresAt,
	}

	err = s.sendMail(ctx, emailPayload)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("failed to send reset token email", append(lf, zap.Error(err))...)
		return err
	}

	return nil
}

func (s *service) sendMail(ctx context.Context, emailPayload presentation.ResetTokenResponse) error {
	var (
		eventName = "resettoken.service.sendMail"
		lf        = []zapcore.Field{
			zap.String("event_name", eventName),
			zap.String("req.email", emailPayload.SendTo),
		}
		sa = []attribute.KeyValue{
			attribute.String("email", emailPayload.SendTo),
		}
	)

	ctx, span := otel.Tracer("resettoken.service").Start(ctx, eventName)
	defer span.End()
	span.SetAttributes(sa...)

	queryVals, err := query.Values(emailPayload)
	if err != nil {
		span.RecordError(err)
		s.logger.Warn("failed to encode query values", append(lf, zap.Error(err))...)
		return err
	}

	resetLink := fmt.Sprintf("%s/reset?%s", s.config.Server.FEHost, queryVals.Encode())
	emailPayload.ResetLink = &resetLink

	err = s.mailer.SendTemplate(ctx, emailPayload.SendTo, "Reset Password", "reset_password", emailPayload)
	if err != nil {
		span.RecordError(err)
		s.logger.Warn("failed to send email", append(lf, zap.Error(err))...)
		return err
	}

	return nil
}
