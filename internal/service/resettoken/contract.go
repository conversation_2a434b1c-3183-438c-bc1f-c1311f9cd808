// Package resettoken defines the reset token service and its methods
package resettoken

import (
	"context"
	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
	"telescope-be/pkg/mailer"

	"go.uber.org/zap"
)

// Service defines the interface for reset token-related operations
type Service interface {
	CreateResetToken(ctx context.Context, req presentation.CreateResetTokenRequest) error
	VerifyResetToken(ctx context.Context, req presentation.VerifyResetTokenRequest) (*presentation.ResetTokenResponse, error)
}

// service implements the Service interface
type service struct {
	config *appctx.Config
	repo   *repository.Repository
	logger *zap.Logger
	mailer mailer.Mailer
}

// NewService creates a new reset token service instance
func NewService(
	config *appctx.Config,
	repo *repository.Repository,
	logger *zap.Logger,
	mailer mailer.Mailer,
) Service {
	return &service{
		config: config,
		repo:   repo,
		logger: logger,
		mailer: mailer,
	}
}
