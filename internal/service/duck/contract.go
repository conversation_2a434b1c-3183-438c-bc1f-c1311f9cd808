// Package duck provides business logic services for duck-related operations.
package duck

import (
	"telescope-be/internal/presentation"
)

type DuckService interface {
	GetDuck() (*presentation.DuckGetResponse, error)
	CreateDuck(request presentation.DuckCreateRequest) (*presentation.DuckCreateResponse, error)
}

type duckService struct{}

func NewDuckService() DuckService {
	return &duckService{}
}
