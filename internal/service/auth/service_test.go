package auth

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap/zaptest"

	"telescope-be/internal/entity"
	"telescope-be/internal/helper"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

// Mock repository for testing
type mockUserRepository struct {
	mock.Mock
}

func (m *mockUserRepository) Register(ctx context.Context, user entity.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *mockUserRepository) CheckUserExistByEmail(ctx context.Context, email string) (bool, error) {
	args := m.Called(ctx, email)
	return args.Bool(0), args.Error(1)
}

func (m *mockUserRepository) GetUserByEmail(ctx context.Context, email string) (*entity.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *mockUserRepository) GetUserByUsername(ctx context.Context, username string) (*entity.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *mockUserRepository) GetUserByID(ctx context.Context, userID int) (*entity.User, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *mockUserRepository) UpdatePassword(ctx context.Context, user entity.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

type mockAuthRepository struct {
	mock.Mock
}

func (m *mockAuthRepository) SaveRefreshToken(ctx context.Context, token entity.RefreshToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *mockAuthRepository) GetRefreshToken(ctx context.Context, tokenHash string) (*entity.RefreshToken, error) {
	args := m.Called(ctx, tokenHash)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.RefreshToken), args.Error(1)
}

func (m *mockAuthRepository) RevokeRefreshToken(ctx context.Context, tokenHash string) error {
	args := m.Called(ctx, tokenHash)
	return args.Error(0)
}

func (m *mockAuthRepository) RevokeAllUserTokens(ctx context.Context, userID string) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func createTestUser() *entity.User {
	hashedPassword, _ := helper.HashPassword("testpassword123")
	countryID := 1

	return &entity.User{
		ID:           1,
		ExternalID:   uuid.New(),
		FirstName:    "Test",
		LastName:     "User",
		Username:     "testuser",
		Email:        "<EMAIL>",
		PasswordHash: hashedPassword,
		CountryID:    &countryID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

func createTestAuthService(t *testing.T) (*authService, *mockUserRepository, *mockAuthRepository) {
	mockUserRepo := &mockUserRepository{}
	mockAuthRepo := &mockAuthRepository{}

	repo := &repository.Repository{
		User: mockUserRepo,
		Auth: mockAuthRepo,
	}

	jwtHelper := helper.NewJWTHelper("test-secret", 15*time.Minute, 7*24*time.Hour)
	logger := zaptest.NewLogger(t)

	service := &authService{
		repo:      repo,
		jwtHelper: jwtHelper,
		logger:    logger,
	}

	return service, mockUserRepo, mockAuthRepo
}

func TestAuthService_Login_WithEmail(t *testing.T) {
	service, mockUserRepo, mockAuthRepo := createTestAuthService(t)
	testUser := createTestUser()

	// Setup mocks
	mockUserRepo.On("GetUserByEmail", mock.Anything, "<EMAIL>").Return(testUser, nil)
	mockAuthRepo.On("SaveRefreshToken", mock.Anything, mock.AnythingOfType("entity.RefreshToken")).Return(nil)

	// Test login with email
	request := presentation.LoginRequest{
		Identifier: "<EMAIL>",
		Password:   "testpassword123",
	}

	response, err := service.Login(context.Background(), request)

	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.AccessToken)
	assert.NotEmpty(t, response.RefreshToken)
	assert.Equal(t, "Bearer", response.TokenType)
	assert.Equal(t, testUser.Email, response.User.Email)
	assert.Equal(t, testUser.Username, response.User.Username)

	mockUserRepo.AssertExpectations(t)
	mockAuthRepo.AssertExpectations(t)
}

func TestAuthService_Login_WithUsername(t *testing.T) {
	service, mockUserRepo, mockAuthRepo := createTestAuthService(t)
	testUser := createTestUser()

	// Setup mocks
	mockUserRepo.On("GetUserByUsername", mock.Anything, "testuser").Return(testUser, nil)
	mockAuthRepo.On("SaveRefreshToken", mock.Anything, mock.AnythingOfType("entity.RefreshToken")).Return(nil)

	// Test login with username
	request := presentation.LoginRequest{
		Identifier: "testuser",
		Password:   "testpassword123",
	}

	response, err := service.Login(context.Background(), request)

	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.AccessToken)
	assert.NotEmpty(t, response.RefreshToken)
	assert.Equal(t, "Bearer", response.TokenType)
	assert.Equal(t, testUser.Email, response.User.Email)
	assert.Equal(t, testUser.Username, response.User.Username)

	mockUserRepo.AssertExpectations(t)
	mockAuthRepo.AssertExpectations(t)
}

func TestAuthService_Login_InvalidIdentifier(t *testing.T) {
	service, _, _ := createTestAuthService(t)

	// Test with invalid identifier
	request := presentation.LoginRequest{
		Identifier: "ab", // too short
		Password:   "testpassword123",
	}

	response, err := service.Login(context.Background(), request)

	assert.Error(t, err)
	assert.Nil(t, response)
	assert.Contains(t, err.Error(), ErrInvalidCredentials)
}

func TestAuthService_Login_UserNotFound(t *testing.T) {
	service, mockUserRepo, _ := createTestAuthService(t)

	// Setup mocks
	mockUserRepo.On("GetUserByEmail", mock.Anything, "<EMAIL>").Return(nil, errors.New("user not found"))

	// Test login with non-existent email
	request := presentation.LoginRequest{
		Identifier: "<EMAIL>",
		Password:   "testpassword123",
	}

	response, err := service.Login(context.Background(), request)

	assert.Error(t, err)
	assert.Nil(t, response)
	assert.Contains(t, err.Error(), ErrInvalidCredentials)

	mockUserRepo.AssertExpectations(t)
}

func TestAuthService_Login_InvalidPassword(t *testing.T) {
	service, mockUserRepo, _ := createTestAuthService(t)
	testUser := createTestUser()

	// Setup mocks
	mockUserRepo.On("GetUserByEmail", mock.Anything, "<EMAIL>").Return(testUser, nil)

	// Test login with wrong password
	request := presentation.LoginRequest{
		Identifier: "<EMAIL>",
		Password:   "wrongpassword",
	}

	response, err := service.Login(context.Background(), request)

	assert.Error(t, err)
	assert.Nil(t, response)
	assert.Contains(t, err.Error(), ErrInvalidCredentials)

	mockUserRepo.AssertExpectations(t)
}

func TestAuthService_GetUserByIdentifier(t *testing.T) {
	service, mockUserRepo, _ := createTestAuthService(t)
	testUser := createTestUser()

	tests := []struct {
		name           string
		identifier     string
		identifierType string
		setupMock      func()
		expectedUser   *entity.User
		expectedError  bool
	}{
		{
			name:           "get user by email",
			identifier:     "<EMAIL>",
			identifierType: "email",
			setupMock: func() {
				mockUserRepo.On("GetUserByEmail", mock.Anything, "<EMAIL>").Return(testUser, nil)
			},
			expectedUser:  testUser,
			expectedError: false,
		},
		{
			name:           "get user by username",
			identifier:     "testuser",
			identifierType: "username",
			setupMock: func() {
				mockUserRepo.On("GetUserByUsername", mock.Anything, "testuser").Return(testUser, nil)
			},
			expectedUser:  testUser,
			expectedError: false,
		},
		{
			name:           "invalid identifier type",
			identifier:     "test",
			identifierType: "invalid",
			setupMock:      func() {},
			expectedUser:   nil,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockUserRepo.ExpectedCalls = nil
			tt.setupMock()

			user, err := service.getUserByIdentifier(context.Background(), tt.identifier, tt.identifierType)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedUser, user)
			}

			mockUserRepo.AssertExpectations(t)
		})
	}
}
