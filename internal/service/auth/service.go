// Package auth provides authentication and authorization services.
package auth

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"strconv"
	"time"

	jwt "github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"

	"telescope-be/internal/entity"
	"telescope-be/internal/helper"
	"telescope-be/internal/presentation"
)

const (
	// Error messages for authentication
	ErrInvalidCredentials  = "invalid credentials"
	ErrInvalidRefreshToken = "invalid refresh token"
)

func (s *authService) Login(ctx context.Context, request presentation.LoginRequest) (*presentation.LoginResponse, error) {
	user, err := s.validateUserCredentials(ctx, request)
	if err != nil {
		return nil, err
	}

	tokens, err := s.generateTokens(user)
	if err != nil {
		return nil, err
	}

	if err := s.saveRefreshToken(ctx, user.ID, tokens.RefreshToken); err != nil {
		s.logger.Error("failed to save refresh token", zap.String("identifier", request.Identifier), zap.Error(err))
		return nil, errors.New("failed to save refresh token")
	}

	return s.buildLoginResponse(user, tokens), nil
}

func (s *authService) validateUserCredentials(ctx context.Context, request presentation.LoginRequest) (*entity.User, error) {
	// Validate and determine identifier type
	identifierType, err := helper.ValidateLoginIdentifier(request.Identifier)
	if err != nil {
		s.logger.Warn("invalid identifier format", zap.String("identifier", request.Identifier), zap.Error(err))
		return nil, errors.New(ErrInvalidCredentials)
	}

	// Sanitize identifier for consistent lookup
	sanitizedIdentifier := helper.SanitizeIdentifier(request.Identifier)

	// Get user based on identifier type
	user, err := s.getUserByIdentifier(ctx, sanitizedIdentifier, identifierType)
	if err != nil {
		s.logger.Error("failed to get user",
			zap.String("identifier", request.Identifier),
			zap.String("type", identifierType),
			zap.Error(err))
		return nil, errors.New(ErrInvalidCredentials)
	}

	if user == nil {
		s.logger.Warn("user not found",
			zap.String("identifier", request.Identifier),
			zap.String("type", identifierType))
		return nil, errors.New(ErrInvalidCredentials)
	}

	// Verify password
	if !helper.CheckPasswordHash(request.Password, user.PasswordHash) {
		s.logger.Warn("invalid password",
			zap.String("identifier", request.Identifier),
			zap.String("type", identifierType))
		return nil, errors.New(ErrInvalidCredentials)
	}

	return user, nil
}

// getUserByIdentifier retrieves user by email or username based on identifier type
func (s *authService) getUserByIdentifier(ctx context.Context, identifier, identifierType string) (*entity.User, error) {
	switch identifierType {
	case "email":
		return s.repo.User.GetUserByEmail(ctx, identifier)
	case "username":
		return s.repo.User.GetUserByUsername(ctx, identifier)
	default:
		return nil, errors.New("invalid identifier type")
	}
}

type tokenPair struct {
	AccessToken  string
	RefreshToken string
}

func (s *authService) generateTokens(user *entity.User) (*tokenPair, error) {
	userIDStr := strconv.Itoa(user.ID)

	accessToken, err := s.jwtHelper.GenerateAccessToken(userIDStr, user.Email)
	if err != nil {
		s.logger.Error("failed to generate access token", zap.String("email", user.Email), zap.Error(err))
		return nil, errors.New("failed to generate access token")
	}

	refreshToken, err := s.jwtHelper.GenerateRefreshToken(userIDStr, user.Email)
	if err != nil {
		s.logger.Error("failed to generate refresh token", zap.String("email", user.Email), zap.Error(err))
		return nil, errors.New("failed to generate refresh token")
	}

	return &tokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}, nil
}

func (s *authService) saveRefreshToken(ctx context.Context, userID int, refreshToken string) error {
	tokenHash := s.hashToken(refreshToken)
	now := time.Now()

	externalID, err := helper.GenerateUUIDv7()
	if err != nil {
		s.logger.Error("failed to generate UUID", zap.Error(err))
		return errors.New("failed to generate token ID")
	}

	refreshTokenEntity := entity.RefreshToken{
		ExternalID: externalID,
		UserID:     userID,
		TokenHash:  tokenHash,
		ExpiresAt:  now.Add(7 * 24 * time.Hour),
		IsRevoked:  false,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	return s.repo.Auth.SaveRefreshToken(ctx, refreshTokenEntity)
}

func (s *authService) buildLoginResponse(user *entity.User, tokens *tokenPair) *presentation.LoginResponse {
	userIDStr := strconv.Itoa(user.ID)
	return &presentation.LoginResponse{
		AccessToken:  tokens.AccessToken,
		RefreshToken: tokens.RefreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    15 * 60,
		User: &presentation.UserInfo{
			ID:       userIDStr,
			Email:    user.Email,
			Username: user.Username,
			CountryID: func() int {
				if user.CountryID != nil {
					return *user.CountryID
				}
				return 0
			}(),
		},
	}
}

func (s *authService) RefreshToken(ctx context.Context, request presentation.RefreshTokenRequest) (*presentation.LoginResponse, error) {
	// Validate refresh token and extract user ID
	userID, err := s.validateRefreshToken(request.RefreshToken)
	if err != nil {
		return nil, err
	}

	// Get user information
	user, err := s.getUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Validate stored refresh token
	tokenHash := s.hashToken(request.RefreshToken)
	if err := s.validateStoredToken(ctx, tokenHash); err != nil {
		return nil, err
	}

	// Generate new tokens
	tokens, err := s.generateTokens(user)
	if err != nil {
		return nil, err
	}

	// Rotate refresh token
	if err := s.rotateRefreshToken(ctx, tokenHash, tokens.RefreshToken, user.ID); err != nil {
		return nil, err
	}

	return s.buildLoginResponse(user, tokens), nil
}

// validateRefreshToken validates the refresh token and returns the user ID
func (s *authService) validateRefreshToken(refreshToken string) (int, error) {
	token, err := jwt.ParseWithClaims(refreshToken, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtHelper.GetSecretKey(), nil
	})
	if err != nil {
		s.logger.Error("failed to parse refresh token", zap.Error(err))
		return 0, errors.New(ErrInvalidRefreshToken)
	}

	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok || !token.Valid {
		s.logger.Warn("invalid refresh token claims")
		return 0, errors.New(ErrInvalidRefreshToken)
	}

	if claims.ExpiresAt.Before(time.Now()) {
		s.logger.Warn("refresh token has expired")
		return 0, errors.New("refresh token has expired")
	}

	userIDStr := claims.Subject
	if userIDStr == "" {
		s.logger.Error("user ID not found in refresh token claims")
		return 0, errors.New(ErrInvalidRefreshToken)
	}

	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		s.logger.Error("invalid user ID in token claims", zap.String("user_id", userIDStr), zap.Error(err))
		return 0, errors.New(ErrInvalidRefreshToken)
	}

	return userID, nil
}

// getUserByID retrieves user information by ID
func (s *authService) getUserByID(ctx context.Context, userID int) (*entity.User, error) {
	userIDStr := strconv.Itoa(userID)
	user, err := s.repo.User.GetUserByID(ctx, userID)
	if err != nil {
		s.logger.Error("failed to get user by ID", zap.String("user_id", userIDStr), zap.Error(err))
		return nil, errors.New("failed to get user information")
	}

	if user == nil {
		s.logger.Warn("user not found", zap.String("user_id", userIDStr))
		return nil, errors.New("user not found")
	}

	return user, nil
}

// validateStoredToken validates the stored refresh token
func (s *authService) validateStoredToken(ctx context.Context, tokenHash string) error {
	storedToken, err := s.repo.Auth.GetRefreshToken(ctx, tokenHash)
	if err != nil {
		s.logger.Error("failed to get refresh token from database", zap.Error(err))
		return errors.New(ErrInvalidRefreshToken)
	}

	if storedToken.IsRevoked {
		s.logger.Warn("refresh token has been revoked", zap.String("token_hash", tokenHash))
		return errors.New("refresh token has been revoked")
	}

	return nil
}

// rotateRefreshToken revokes the old token and saves the new one
func (s *authService) rotateRefreshToken(ctx context.Context, oldTokenHash, newRefreshToken string, userID int) error {
	// Revoke old token
	if err := s.repo.Auth.RevokeRefreshToken(ctx, oldTokenHash); err != nil {
		s.logger.Error("failed to revoke old refresh token", zap.String("token_hash", oldTokenHash), zap.Error(err))
	}

	// Save new token
	newTokenHash := s.hashToken(newRefreshToken)
	now := time.Now()

	newExternalID, err := helper.GenerateUUIDv7()
	if err != nil {
		s.logger.Error("failed to generate UUID", zap.Error(err))
		return errors.New("failed to generate token ID")
	}

	newRefreshTokenEntity := entity.RefreshToken{
		ExternalID: newExternalID,
		UserID:     userID,
		TokenHash:  newTokenHash,
		ExpiresAt:  now.Add(7 * 24 * time.Hour),
		IsRevoked:  false,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	if err := s.repo.Auth.SaveRefreshToken(ctx, newRefreshTokenEntity); err != nil {
		s.logger.Error("failed to save new refresh token", zap.Error(err))
		return errors.New("failed to save new refresh token")
	}

	return nil
}

func (s *authService) Logout(ctx context.Context, request presentation.RefreshTokenRequest) error {
	tokenHash := s.hashToken(request.RefreshToken)

	if err := s.repo.Auth.RevokeRefreshToken(ctx, tokenHash); err != nil {
		s.logger.Error("failed to revoke refresh token", zap.String("token_hash", tokenHash), zap.Error(err))
		return errors.New("failed to logout")
	}

	return nil
}

func (s *authService) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}
