package auth

import (
	"context"

	"go.uber.org/zap"

	"telescope-be/internal/helper"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

type AuthService interface {
	Login(ctx context.Context, request presentation.LoginRequest) (*presentation.LoginResponse, error)
	RefreshToken(ctx context.Context, request presentation.RefreshTokenRequest) (*presentation.LoginResponse, error)
	Logout(ctx context.Context, request presentation.RefreshTokenRequest) error
}

type authService struct {
	repo      *repository.Repository
	jwtHelper *helper.JWTHelper
	logger    *zap.Logger
}

func NewAuthService(repo *repository.Repository, jwtHelper *helper.J<PERSON><PERSON><PERSON><PERSON>, logger *zap.Logger) AuthService {
	return &authService{
		repo:      repo,
		jwtHelper: jwtHelper,
		logger:    logger,
	}
}
