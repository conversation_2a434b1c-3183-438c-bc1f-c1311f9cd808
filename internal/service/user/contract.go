package user

import (
	"context"

	"go.uber.org/zap"

	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

type UserService interface {
	RegisterUser(ctx context.Context, request presentation.UserRequest) (*presentation.UserRegisterResponse, error)
}

type userService struct {
	repo   *repository.Repository
	logger *zap.Logger
}

func NewUserService(repo *repository.Repository, logger *zap.Logger) UserService {
	return &userService{
		repo:   repo,
		logger: logger,
	}
}
