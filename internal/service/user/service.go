// Package user provides user-related business logic and services.
package user

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"

	"telescope-be/internal/entity"
	"telescope-be/internal/helper"
	"telescope-be/internal/presentation"
)

func (s *userService) RegisterUser(ctx context.Context, request presentation.UserRequest) (*presentation.UserRegisterResponse, error) {
	if err := s.checkUserExists(ctx, request.Email); err != nil {
		return nil, err
	}

	user, err := s.createUserEntity(request)
	if err != nil {
		s.logger.Error("failed to create user entity",
			zap.String("email", request.Email),
			zap.Error(err))
		return nil, err
	}

	if err := s.repo.User.Register(ctx, *user); err != nil {
		s.logger.Error("failed to register user",
			zap.String("email", request.Email),
			zap.Error(err))
		return nil, err
	}

	return s.buildUserResponse(user), nil
}

func (s *userService) checkUserExists(ctx context.Context, email string) error {
	exists, err := s.repo.User.CheckUserExistByEmail(ctx, email)
	if err != nil {
		s.logger.Error("failed to check if user exists",
			zap.String("email", email),
			zap.Error(err))
		return err
	}

	if exists {
		s.logger.Warn("attempt to register existing user",
			zap.String("email", email))
		return errors.New("account already exists")
	}

	return nil
}

func (s *userService) createUserEntity(request presentation.UserRequest) (*entity.User, error) {
	status := "non-active" // TODO: change after the status column on table users is defined

	externalID, err := helper.GenerateUUIDv7()
	if err != nil {
		return nil, err
	}

	hashedPassword, err := helper.HashPassword(request.Password)
	if err != nil {
		return nil, err
	}

	tNow := time.Now()
	user := &entity.User{
		ExternalID:   externalID,
		FirstName:    "", // TODO: Add to UserRequest if needed
		LastName:     "", // TODO: Add to UserRequest if needed
		Email:        request.Email,
		Username:     request.Username,
		CountryID:    &request.CountryID,
		Status:       &status,
		PasswordHash: hashedPassword,
		CreatedAt:    tNow,
		UpdatedAt:    tNow,
	}

	return user, nil
}

func (s *userService) buildUserResponse(user *entity.User) *presentation.UserRegisterResponse {
	return &presentation.UserRegisterResponse{
		ID:       user.ExternalID.String(),
		Email:    user.Email,
		Username: user.Username,
		CountryID: func() int {
			if user.CountryID != nil {
				return *user.CountryID
			}
			return 0
		}(),
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}
}
