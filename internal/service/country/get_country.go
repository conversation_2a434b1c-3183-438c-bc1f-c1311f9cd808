package country

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	pgx "github.com/jackc/pgx/v5"

	"telescope-be/internal/presentation"
)

func (s *countryService) GetByID(ctx context.Context, externalID uuid.UUID) (*presentation.CountryResponse, error) {
	country, err := s.countryRepo.GetByExternalID(ctx, externalID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("country not found")
		}
		return nil, fmt.Errorf("failed to get country: %w", err)
	}

	return &presentation.CountryResponse{
		ID:                country.ExternalID,
		Code:              country.Code,
		Name:              country.Name,
		FlagEmoji:         country.FlagEmoji,
		IsActive:          country.IsActive,
		SupportsSending:   country.SupportsSending,
		SupportsReceiving: country.SupportsReceiving,
		CreatedAt:         country.CreatedAt,
		UpdatedAt:         country.UpdatedAt,
	}, nil
}

func (s *countryService) List(ctx context.Context, activeOnly bool) (*presentation.CountryListResponse, error) {
	countries, err := s.countryRepo.List(ctx, activeOnly)
	if err != nil {
		return nil, fmt.Errorf("failed to list countries: %w", err)
	}

	responses := make([]presentation.CountryResponse, len(countries))
	for i, country := range countries {
		responses[i] = presentation.CountryResponse{
			ID:                country.ExternalID,
			Code:              country.Code,
			Name:              country.Name,
			FlagEmoji:         country.FlagEmoji,
			IsActive:          country.IsActive,
			SupportsSending:   country.SupportsSending,
			SupportsReceiving: country.SupportsReceiving,
			CreatedAt:         country.CreatedAt,
			UpdatedAt:         country.UpdatedAt,
		}
	}

	return &presentation.CountryListResponse{
		Countries: responses,
		Total:     len(responses),
	}, nil
}
