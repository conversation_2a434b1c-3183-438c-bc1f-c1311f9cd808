package country

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	pgx "github.com/jackc/pgx/v5"

	"telescope-be/internal/audit"
	"telescope-be/internal/presentation"
)

func (s *countryService) Update(
	ctx context.Context,
	externalID uuid.UUID,
	req *presentation.CountryUpdateRequest,
) (*presentation.CountryResponse, error) {
	// Get existing country
	existing, err := s.countryRepo.GetByExternalID(ctx, externalID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("country not found")
		}
		return nil, fmt.Errorf("failed to get country: %w", err)
	}

	// Store old values for audit
	oldValues := map[string]any{
		"name":               existing.Name,
		"flag_emoji":         existing.FlagEmoji,
		"supports_sending":   existing.SupportsSending,
		"supports_receiving": existing.SupportsReceiving,
	}

	// Update fields
	existing.Name = req.Name
	existing.FlagEmoji = req.FlagEmoji
	existing.SupportsSending = req.SupportsSending
	existing.SupportsReceiving = req.SupportsReceiving
	// TODO: Set UpdatedBy from user context

	// Save changes
	updatedCountry, err := s.countryRepo.Update(ctx, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update country: %w", err)
	}

	// Log audit event in goroutine
	go func() {
		newValues := map[string]any{
			"name":               updatedCountry.Name,
			"flag_emoji":         updatedCountry.FlagEmoji,
			"supports_sending":   updatedCountry.SupportsSending,
			"supports_receiving": updatedCountry.SupportsReceiving,
		}
		// TODO: Extract user ID from context when JWT auth is implemented
		s.auditLogger.LogEvent(ctx, audit.AuditEvent{
			UserID:     0, // Default for unauthenticated users
			Operation:  audit.OpUpdate,
			Resource:   audit.ResourceCountry,
			ResourceID: updatedCountry.ExternalID.String(),
			OldValues:  oldValues,
			NewValues:  newValues,
			Success:    true,
		})
	}()

	return &presentation.CountryResponse{
		ID:                updatedCountry.ExternalID,
		Code:              updatedCountry.Code,
		Name:              updatedCountry.Name,
		FlagEmoji:         updatedCountry.FlagEmoji,
		IsActive:          updatedCountry.IsActive,
		SupportsSending:   updatedCountry.SupportsSending,
		SupportsReceiving: updatedCountry.SupportsReceiving,
		CreatedAt:         updatedCountry.CreatedAt,
		UpdatedAt:         updatedCountry.UpdatedAt,
	}, nil
}

func (s *countryService) ToggleStatus(ctx context.Context, externalID uuid.UUID, req *presentation.CountryToggleRequest) error {
	err := s.countryRepo.UpdateStatus(ctx, externalID, req.IsActive)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("country not found")
		}
		return fmt.Errorf("failed to toggle country status: %w", err)
	}

	// Log audit event in goroutine
	go func() {
		// TODO: Extract user ID from context when JWT auth is implemented
		s.auditLogger.LogEvent(ctx, audit.AuditEvent{
			UserID:     0, // Default for unauthenticated users
			Operation:  audit.OpStatusToggle,
			Resource:   audit.ResourceCountry,
			ResourceID: externalID.String(),
			NewValues:  map[string]any{"is_active": req.IsActive},
			Success:    true,
		})
	}()

	return nil
}
