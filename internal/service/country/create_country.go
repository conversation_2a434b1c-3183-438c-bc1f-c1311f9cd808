package country

import (
	"context"
	"fmt"

	"telescope-be/internal/audit"
	"telescope-be/internal/entity"
	"telescope-be/internal/presentation"
)

func (s *countryService) Create(ctx context.Context, req *presentation.CountryCreateRequest) (*presentation.CountryResponse, error) {
	// Check if country with same code already exists
	exists, err := s.countryRepo.ExistsByCode(ctx, req.Code)
	if err != nil {
		return nil, fmt.Errorf("failed to check country existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("country with code %s already exists", req.Code)
	}

	// Create entity
	country := &entity.Country{
		Code:              req.Code,
		Name:              req.Name,
		FlagEmoji:         req.FlagEmoji,
		IsActive:          req.IsActive,
		SupportsSending:   req.SupportsSending,
		SupportsReceiving: req.SupportsReceiving,
		// TODO: Set CreatedBy and UpdatedBy from user context
	}

	// Save to database
	savedCountry, err := s.countryRepo.Create(ctx, country)
	if err != nil {
		return nil, fmt.Errorf("failed to create country: %w", err)
	}

	// Log audit event in goroutine
	go func() {
		values := map[string]any{
			"code":               savedCountry.Code,
			"name":               savedCountry.Name,
			"flag_emoji":         savedCountry.FlagEmoji,
			"is_active":          savedCountry.IsActive,
			"supports_sending":   savedCountry.SupportsSending,
			"supports_receiving": savedCountry.SupportsReceiving,
		}
		// TODO: Extract user ID from context when JWT auth is implemented
		s.auditLogger.LogEvent(ctx, audit.AuditEvent{
			UserID:     0, // Default for unauthenticated users
			Operation:  audit.OpCreate,
			Resource:   audit.ResourceCountry,
			ResourceID: savedCountry.ExternalID.String(),
			NewValues:  values,
			Success:    true,
		})
	}()

	// Convert to response
	return &presentation.CountryResponse{
		ID:                savedCountry.ExternalID,
		Code:              savedCountry.Code,
		Name:              savedCountry.Name,
		FlagEmoji:         savedCountry.FlagEmoji,
		IsActive:          savedCountry.IsActive,
		SupportsSending:   savedCountry.SupportsSending,
		SupportsReceiving: savedCountry.SupportsReceiving,
		CreatedAt:         savedCountry.CreatedAt,
		UpdatedAt:         savedCountry.UpdatedAt,
	}, nil
}
