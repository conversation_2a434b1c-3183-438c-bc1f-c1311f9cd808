// Package country provides business logic services for country operations.
package country

import (
	"context"

	"github.com/google/uuid"

	"telescope-be/internal/audit"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

type CountryService interface {
	Create(ctx context.Context, req *presentation.CountryCreateRequest) (*presentation.CountryResponse, error)
	GetByID(ctx context.Context, externalID uuid.UUID) (*presentation.CountryResponse, error)
	List(ctx context.Context, activeOnly bool) (*presentation.CountryListResponse, error)
	Update(ctx context.Context, externalID uuid.UUID, req *presentation.CountryUpdateRequest) (*presentation.CountryResponse, error)
	ToggleStatus(ctx context.Context, externalID uuid.UUID, req *presentation.CountryToggleRequest) error
}

type countryService struct {
	countryRepo repository.CountryRepository
	auditLogger audit.Logger
}

func NewCountryService(countryRepo repository.CountryRepository, auditLogger audit.Logger) CountryService {
	return &countryService{
		countryRepo: countryRepo,
		auditLogger: auditLogger,
	}
}
