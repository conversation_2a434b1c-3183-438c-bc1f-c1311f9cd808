package exchangerate

import (
	"context"
	"errors"
	"fmt"

	pgx "github.com/jackc/pgx/v5"

	"telescope-be/internal/entity"
	"telescope-be/internal/presentation"
)

func (s *exchangeRateService) GetCurrentRate(
	ctx context.Context,
	fromCurrency, toCurrency string,
) (*presentation.ExchangeRateResponse, error) {
	rate, err := s.exchangeRateRepo.GetCurrentRate(ctx, fromCurrency, toCurrency)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("exchange rate not found for %s to %s", fromCurrency, toCurrency)
		}
		return nil, fmt.Errorf("failed to get exchange rate: %w", err)
	}

	return s.mapToResponse(rate), nil
}

func (s *exchangeRateService) GetCurrentRates(ctx context.Context, activeOnly bool) (*presentation.ExchangeRateListResponse, error) {
	rates, err := s.exchangeRateRepo.GetCurrentRates(ctx, activeOnly)
	if err != nil {
		return nil, fmt.Errorf("failed to get current rates: %w", err)
	}

	responses := make([]presentation.ExchangeRateResponse, len(rates))
	for i, rate := range rates {
		responses[i] = *s.mapToResponse(rate)
	}

	return &presentation.ExchangeRateListResponse{
		Rates: responses,
		Total: len(responses),
	}, nil
}

func (s *exchangeRateService) GetRateHistory(
	ctx context.Context,
	fromCurrency, toCurrency string,
	limit int,
) (*presentation.ExchangeRateHistoryResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 50 // Default limit
	}

	rates, err := s.exchangeRateRepo.GetRateHistory(ctx, fromCurrency, toCurrency, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get rate history: %w", err)
	}

	responses := make([]presentation.ExchangeRateResponse, len(rates))
	for i, rate := range rates {
		responses[i] = *s.mapToResponse(rate)
	}

	return &presentation.ExchangeRateHistoryResponse{
		Rates: responses,
		Total: len(responses),
	}, nil
}

func (s *exchangeRateService) mapToResponse(rate *entity.ExchangeRate) *presentation.ExchangeRateResponse {
	response := &presentation.ExchangeRateResponse{
		ID:         rate.ExternalID,
		Rate:       rate.Rate,
		Source:     rate.Source,
		ValidFrom:  rate.ValidFrom,
		ValidUntil: rate.ValidUntil,
		CreatedAt:  rate.CreatedAt,
	}

	// Map currencies
	if rate.CurrencyPair != nil {
		if rate.CurrencyPair.FromCurrency != nil {
			response.FromCurrency = presentation.CurrencyResponse{
				ID:            rate.CurrencyPair.FromCurrency.ExternalID,
				Code:          rate.CurrencyPair.FromCurrency.Code,
				Name:          rate.CurrencyPair.FromCurrency.Name,
				Symbol:        rate.CurrencyPair.FromCurrency.Symbol,
				DecimalPlaces: rate.CurrencyPair.FromCurrency.DecimalPlaces,
				IsActive:      rate.CurrencyPair.FromCurrency.IsActive,
				CreatedAt:     rate.CurrencyPair.FromCurrency.CreatedAt,
				UpdatedAt:     rate.CurrencyPair.FromCurrency.UpdatedAt,
			}
		}

		if rate.CurrencyPair.ToCurrency != nil {
			response.ToCurrency = presentation.CurrencyResponse{
				ID:            rate.CurrencyPair.ToCurrency.ExternalID,
				Code:          rate.CurrencyPair.ToCurrency.Code,
				Name:          rate.CurrencyPair.ToCurrency.Name,
				Symbol:        rate.CurrencyPair.ToCurrency.Symbol,
				DecimalPlaces: rate.CurrencyPair.ToCurrency.DecimalPlaces,
				IsActive:      rate.CurrencyPair.ToCurrency.IsActive,
				CreatedAt:     rate.CurrencyPair.ToCurrency.CreatedAt,
				UpdatedAt:     rate.CurrencyPair.ToCurrency.UpdatedAt,
			}
		}
	}

	return response
}
