// Package exchangerate provides business logic services for exchange rate operations.
package exchangerate

import (
	"context"

	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

type ExchangeRateService interface {
	GetCurrentRate(ctx context.Context, fromCurrency, toCurrency string) (*presentation.ExchangeRateResponse, error)
	GetCurrentRates(ctx context.Context, activeOnly bool) (*presentation.ExchangeRateListResponse, error)
	GetRateHistory(ctx context.Context, fromCurrency, toCurrency string, limit int) (*presentation.ExchangeRateHistoryResponse, error)
}

type exchangeRateService struct {
	exchangeRateRepo repository.ExchangeRateRepository
}

func NewExchangeRateService(exchangeRateRepo repository.ExchangeRateRepository) ExchangeRateService {
	return &exchangeRateService{
		exchangeRateRepo: exchangeRateRepo,
	}
}
