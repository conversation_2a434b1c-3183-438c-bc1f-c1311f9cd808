package currency

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
	pgx "github.com/jackc/pgx/v5"

	"telescope-be/internal/audit"
	"telescope-be/internal/entity"
	"telescope-be/internal/presentation"
)

func (s *currencyService) Create(ctx context.Context, req *presentation.CurrencyCreateRequest) (*presentation.CurrencyResponse, error) {
	// Check if currency with same code already exists
	exists, err := s.currencyRepo.ExistsByCode(ctx, req.Code)
	if err != nil {
		return nil, fmt.Errorf("failed to check currency existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("currency with code %s already exists", req.Code)
	}

	// Validate country if provided
	countryID, countryResponse, err := s.validateCountry(ctx, req.CountryID)
	if err != nil {
		return nil, err
	}

	// Create entity
	currency := &entity.Currency{
		Code:          req.Code,
		Name:          req.Name,
		Symbol:        req.Symbol,
		DecimalPlaces: req.DecimalPlaces,
		CountryID:     countryID,
		IsActive:      req.IsActive,
		// TODO: Set CreatedBy and UpdatedBy from user context
	}

	// Save to database
	savedCurrency, err := s.currencyRepo.Create(ctx, currency)
	if err != nil {
		return nil, fmt.Errorf("failed to create currency: %w", err)
	}

	// Log audit event
	s.logCurrencyAudit(ctx, savedCurrency, audit.OpCreate, nil)

	return s.buildCurrencyResponse(savedCurrency, countryResponse), nil
}

func (s *currencyService) GetByID(ctx context.Context, externalID uuid.UUID) (*presentation.CurrencyResponse, error) {
	currency, err := s.currencyRepo.GetByExternalID(ctx, externalID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("currency not found")
		}
		return nil, fmt.Errorf("failed to get currency: %w", err)
	}

	response := &presentation.CurrencyResponse{
		ID:            currency.ExternalID,
		Code:          currency.Code,
		Name:          currency.Name,
		Symbol:        currency.Symbol,
		DecimalPlaces: currency.DecimalPlaces,
		IsActive:      currency.IsActive,
		CreatedAt:     currency.CreatedAt,
		UpdatedAt:     currency.UpdatedAt,
	}

	// Map country if exists
	if currency.Country != nil {
		response.Country = &presentation.CountryResponse{
			ID:                currency.Country.ExternalID,
			Code:              currency.Country.Code,
			Name:              currency.Country.Name,
			FlagEmoji:         currency.Country.FlagEmoji,
			IsActive:          currency.Country.IsActive,
			SupportsSending:   currency.Country.SupportsSending,
			SupportsReceiving: currency.Country.SupportsReceiving,
			CreatedAt:         currency.Country.CreatedAt,
			UpdatedAt:         currency.Country.UpdatedAt,
		}
	}

	return response, nil
}

func (s *currencyService) List(ctx context.Context, activeOnly bool, offset, limit int) (*presentation.CurrencyListResponse, error) {
	currencies, err := s.currencyRepo.List(ctx, activeOnly, offset, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to list currencies: %w", err)
	}

	responses := make([]presentation.CurrencyResponse, len(currencies))
	for i, currency := range currencies {
		responses[i] = presentation.CurrencyResponse{
			ID:            currency.ExternalID,
			Code:          currency.Code,
			Name:          currency.Name,
			Symbol:        currency.Symbol,
			DecimalPlaces: currency.DecimalPlaces,
			IsActive:      currency.IsActive,
			CreatedAt:     currency.CreatedAt,
			UpdatedAt:     currency.UpdatedAt,
		}

		// Map country if exists
		if currency.Country != nil {
			responses[i].Country = &presentation.CountryResponse{
				ID:                currency.Country.ExternalID,
				Code:              currency.Country.Code,
				Name:              currency.Country.Name,
				FlagEmoji:         currency.Country.FlagEmoji,
				IsActive:          currency.Country.IsActive,
				SupportsSending:   currency.Country.SupportsSending,
				SupportsReceiving: currency.Country.SupportsReceiving,
				CreatedAt:         currency.Country.CreatedAt,
				UpdatedAt:         currency.Country.UpdatedAt,
			}
		}
	}

	return &presentation.CurrencyListResponse{
		Currencies: responses,
		Total:      len(responses),
	}, nil
}

// validateCountry validates the country if provided and returns the country ID and response
func (s *currencyService) validateCountry(ctx context.Context, countryID *uuid.UUID) (*int, *presentation.CountryResponse, error) {
	if countryID == nil {
		return nil, nil, nil
	}

	country, err := s.countryRepo.GetByExternalID(ctx, *countryID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil, fmt.Errorf("country not found")
		}
		return nil, nil, fmt.Errorf("failed to validate country: %w", err)
	}

	countryResponse := &presentation.CountryResponse{
		ID:                country.ExternalID,
		Code:              country.Code,
		Name:              country.Name,
		FlagEmoji:         country.FlagEmoji,
		IsActive:          country.IsActive,
		SupportsSending:   country.SupportsSending,
		SupportsReceiving: country.SupportsReceiving,
		CreatedAt:         country.CreatedAt,
		UpdatedAt:         country.UpdatedAt,
	}

	return &country.ID, countryResponse, nil
}

// buildCurrencyResponse builds a currency response with optional country
func (s *currencyService) buildCurrencyResponse(
	currency *entity.Currency,
	country *presentation.CountryResponse,
) *presentation.CurrencyResponse {
	return &presentation.CurrencyResponse{
		ID:            currency.ExternalID,
		Code:          currency.Code,
		Name:          currency.Name,
		Symbol:        currency.Symbol,
		DecimalPlaces: currency.DecimalPlaces,
		Country:       country,
		IsActive:      currency.IsActive,
		CreatedAt:     currency.CreatedAt,
		UpdatedAt:     currency.UpdatedAt,
	}
}

// buildCurrencyResponseFromEntity builds a currency response from entity with country mapping
func (s *currencyService) buildCurrencyResponseFromEntity(currency *entity.Currency) *presentation.CurrencyResponse {
	response := &presentation.CurrencyResponse{
		ID:            currency.ExternalID,
		Code:          currency.Code,
		Name:          currency.Name,
		Symbol:        currency.Symbol,
		DecimalPlaces: currency.DecimalPlaces,
		IsActive:      currency.IsActive,
		CreatedAt:     currency.CreatedAt,
		UpdatedAt:     currency.UpdatedAt,
	}

	if currency.Country != nil {
		response.Country = &presentation.CountryResponse{
			ID:                currency.Country.ExternalID,
			Code:              currency.Country.Code,
			Name:              currency.Country.Name,
			FlagEmoji:         currency.Country.FlagEmoji,
			IsActive:          currency.Country.IsActive,
			SupportsSending:   currency.Country.SupportsSending,
			SupportsReceiving: currency.Country.SupportsReceiving,
			CreatedAt:         currency.Country.CreatedAt,
			UpdatedAt:         currency.Country.UpdatedAt,
		}
	}

	return response
}

// buildAuditValues builds audit values map from currency entity
func (s *currencyService) buildAuditValues(currency *entity.Currency) map[string]any {
	return map[string]any{
		"name":           currency.Name,
		"symbol":         currency.Symbol,
		"decimal_places": currency.DecimalPlaces,
		"is_active":      currency.IsActive,
	}
}

// logCurrencyAudit logs currency audit events in a goroutine
func (s *currencyService) logCurrencyAudit(ctx context.Context, currency *entity.Currency, operation string, oldValues map[string]any) {
	go func() {
		values := s.buildAuditValues(currency)
		if currency.CountryID != nil {
			values["country_id"] = *currency.CountryID
		}

		auditEvent := audit.AuditEvent{
			UserID:     0, // TODO: Extract user ID from context when JWT auth is implemented
			Operation:  operation,
			Resource:   audit.ResourceCurrency,
			ResourceID: currency.ExternalID.String(),
			NewValues:  values,
			Success:    true,
		}

		if oldValues != nil {
			auditEvent.OldValues = oldValues
		}

		s.auditLogger.LogEvent(ctx, auditEvent)
	}()
}

func (s *currencyService) Update(
	ctx context.Context,
	externalID uuid.UUID,
	req *presentation.CurrencyUpdateRequest,
) (*presentation.CurrencyResponse, error) {
	// Get existing currency
	existing, err := s.currencyRepo.GetByExternalID(ctx, externalID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("currency not found")
		}
		return nil, fmt.Errorf("failed to get currency: %w", err)
	}

	// Store old values for audit
	oldValues := s.buildAuditValues(existing)

	// Update fields
	existing.Name = req.Name
	existing.Symbol = req.Symbol
	existing.DecimalPlaces = req.DecimalPlaces
	existing.IsActive = req.IsActive
	// TODO: Set UpdatedBy from user context

	// Save changes
	updatedCurrency, err := s.currencyRepo.Update(ctx, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update currency: %w", err)
	}

	// Log audit event
	s.logCurrencyAudit(ctx, updatedCurrency, audit.OpUpdate, oldValues)

	return s.buildCurrencyResponseFromEntity(updatedCurrency), nil
}
