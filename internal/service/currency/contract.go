// Package currency provides business logic services for currency operations.
package currency

import (
	"context"

	"github.com/google/uuid"

	"telescope-be/internal/audit"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
)

type CurrencyService interface {
	Create(ctx context.Context, req *presentation.CurrencyCreateRequest) (*presentation.CurrencyResponse, error)
	GetByID(ctx context.Context, externalID uuid.UUID) (*presentation.CurrencyResponse, error)
	List(ctx context.Context, activeOnly bool, offset, limit int) (*presentation.CurrencyListResponse, error)
	Update(ctx context.Context, externalID uuid.UUID, req *presentation.CurrencyUpdateRequest) (*presentation.CurrencyResponse, error)
}

type currencyService struct {
	currencyRepo repository.CurrencyRepository
	countryRepo  repository.CountryRepository
	auditLogger  audit.Logger
}

func NewCurrencyService(
	currencyRepo repository.CurrencyRepository,
	countryRepo repository.CountryRepository,
	auditLogger audit.Logger,
) CurrencyService {
	return &currencyService{
		currencyRepo: currencyRepo,
		countryRepo:  countryRepo,
		auditLogger:  auditLogger,
	}
}
