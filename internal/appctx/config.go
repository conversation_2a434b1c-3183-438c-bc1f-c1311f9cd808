package appctx

type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Logger   LoggerConfig   `yaml:"logger"`
	Provider ProviderConfig `yaml:"provider"`
	Mailer   MailerConfig   `yaml:"mailer"`
	JWT      JWTConfig      `yaml:"jwt"`
	Feature  FeatureConfig  `yaml:"feature"`
}

type JWTConfig struct {
	SecretKey            string `yaml:"secret_key"`
	AccessTokenDuration  string `yaml:"access_token_duration"`
	RefreshTokenDuration string `yaml:"refresh_token_duration"`
}

type ServerConfig struct {
	Host           string   `yaml:"host" default:"127.0.0.1"`
	Port           string   `yaml:"port" default:"8080"`
	Mode           string   `yaml:"mode" default:"release"` // debug, release, test
	AllowedOrigins []string `yaml:"allowed_origins"`
	FEHost         string   `yaml:"fe_host" default:"http://localhost:3000"`
}

type DatabaseConfig struct {
	DSN             string `yaml:"dsn" default:"postgresql://user:password@localhost:5432/telescope"`
	MaxOpenConns    int32  `yaml:"max_open_conns" default:"25"`
	MaxIdleConns    int32  `yaml:"max_idle_conns" default:"5"`
	ConnMaxLifetime string `yaml:"conn_max_lifetime" default:"15m"`
}

type LoggerConfig struct {
	Level  string `yaml:"level" default:"INFO"`
	Format string `yaml:"format" default:"json"` // json, text
}

type MailerConfig struct {
	Provider      string `yaml:"provider"`
	APIKey        string `yaml:"api_key"`
	FromEmail     string `yaml:"from_email"`
	FromName      string `yaml:"from_name" default:"No Reply"`
	TemplatesPath string `yaml:"templates_path"`
	Debug         bool   `yaml:"debug" default:"false"`
}

type ProviderConfig struct {
	IDRX IDRXConfig `yaml:"idrx"`
}

type IDRXConfig struct {
	BaseURL string `yaml:"base_url"`
	Timeout string `yaml:"timeout" default:"10s"`
}

type FeatureConfig struct {
	APIDocs bool `yaml:"api_docs" default:"false"`
}
