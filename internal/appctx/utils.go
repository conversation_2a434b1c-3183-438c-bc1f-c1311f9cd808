// Package appctx provides application context utilities and configuration management.
package appctx

import (
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
	yaml "gopkg.in/yaml.v3"

	"telescope-be/internal/constants"
)

func LoadConfig() (*Config, error) {
	var config Config
	configPath := getConfigPath()

	// Validate config path for security
	if err := validateConfigPath(configPath); err != nil {
		return nil, fmt.Errorf("invalid config path: %w", err)
	}

	_, err := os.Stat(configPath)
	if err == nil {
		data, err := os.ReadFile(configPath) //nolint:gosec // Path is validated by validateConfigPath function
		if err != nil {
			return nil, err
		}

		if err := yaml.Unmarshal(data, &config); err != nil {
			return nil, err
		}
	} else {
		fmt.Fprintf(os.Stderr, "WARN: loading config file %v\n", err)
	}

	overrideWithEnvVars(&config)
	return &config, nil
}

// validateConfigPath validates the config path to prevent directory traversal attacks
func validateConfigPath(path string) error {
	// Clean the path to resolve any . or .. components
	cleanPath := filepath.Clean(path)

	// Check for directory traversal attempts
	if strings.Contains(cleanPath, "..") {
		return fmt.Errorf("path traversal detected")
	}

	// Ensure the path ends with .yaml or .yml for config files
	if !strings.HasSuffix(cleanPath, ".yaml") && !strings.HasSuffix(cleanPath, ".yml") {
		return fmt.Errorf("config file must have .yaml or .yml extension")
	}

	// Convert to absolute path to check if it's within allowed directories
	absPath, err := filepath.Abs(cleanPath)
	if err != nil {
		return fmt.Errorf("failed to resolve absolute path: %w", err)
	}

	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get current directory: %w", err)
	}

	// Ensure the config file is within the current working directory or its subdirectories
	relPath, err := filepath.Rel(cwd, absPath)
	if err != nil || strings.HasPrefix(relPath, "..") {
		return fmt.Errorf("config file must be within the project directory")
	}

	return nil
}

func CreateExampleConfig() error {
	// Create a default config instance with default values from struct tags
	defaultConfig := &Config{}
	applyDefaultValues(reflect.ValueOf(defaultConfig).Elem())

	// Generate config.dist.yaml
	configDist, err := generateConfigDist(defaultConfig)
	if err != nil {
		return err
	}

	// Generate .dist.env
	envDist := generateEnvDist(defaultConfig)

	// Write config.dist.yaml
	err = os.WriteFile("config.dist.yaml", []byte(configDist), 0o600)
	if err != nil {
		return err
	}

	// Write .dist.env
	err = os.WriteFile(".dist.env", []byte(envDist), 0o600)
	if err != nil {
		return err
	}

	return nil
}

func generateConfigDist(config *Config) (string, error) {
	// Add header comment
	result := "# Example configuration file - copy to config.yaml and modify as needed\n"
	result += "# This file is auto-generated from the Config struct definition\n\n"

	// Marshal the config to YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return "", err
	}

	result += string(data)
	return result, nil
}

func generateEnvDist(config *Config) string {
	result := "# Example environment variables file - copy to .env and modify as needed\n"
	result += "# This file is auto-generated from the Config struct definition\n"
	result += "# Copy this file to .env and fill in your actual values\n\n"

	// Generate environment variables recursively
	envVars := generateEnvVarsRecursive(reflect.ValueOf(config).Elem(), strings.ToUpper(constants.AppName))

	// Sort and format the environment variables
	for _, envVar := range envVars {
		result += envVar + "\n"
	}

	return result
}

func generateEnvVarsRecursive(v reflect.Value, prefix string) []string {
	var envVars []string

	if !v.IsValid() {
		return envVars
	}

	//nolint:exhaustive // Only handling struct types intentionally
	switch v.Kind() {
	case reflect.Struct:
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			fieldType := v.Type().Field(i)

			// Get the yaml tag name, fallback to struct field name
			yamlTag := fieldType.Tag.Get("yaml")
			fieldName := strings.Split(yamlTag, ",")[0]
			if fieldName == "" || fieldName == "-" {
				fieldName = strings.ToLower(fieldType.Name)
			}

			// Build the environment variable name
			var envKey string
			if prefix == "" {
				envKey = strings.ToUpper(fieldName)
			} else {
				envKey = prefix + "__" + strings.ToUpper(fieldName)
			}

			// Handle nested structs
			if field.Kind() == reflect.Struct {
				nestedVars := generateEnvVarsRecursive(field, envKey)
				envVars = append(envVars, nestedVars...)
				envVars = append(envVars, "")
				continue
			}

			// Generate environment variable line
			envVar := envKey + "=" + getFieldValueString(field)
			envVars = append(envVars, envVar)
		}
	default:
	}

	return envVars
}

func getFieldValueString(field reflect.Value) string {
	if !field.IsValid() {
		return ""
	}

	//nolint:exhaustive // Only handling specific types we need
	switch field.Kind() {
	case reflect.String:
		return field.String()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(field.Int(), 10)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.FormatUint(field.Uint(), 10)
	case reflect.Float32, reflect.Float64:
		return strconv.FormatFloat(field.Float(), 'f', -1, 64)
	case reflect.Bool:
		return strconv.FormatBool(field.Bool())
	case reflect.Slice:
		// Handle string slices (like AllowedOrigins)
		if field.Type().Elem().Kind() == reflect.String {
			if slice, ok := field.Interface().([]string); ok {
				return strings.Join(slice, ",")
			}
		}
	default:
	}

	return ""
}

func getConfigPath() string {
	if path := os.Getenv("CONFIG_PATH"); path != "" {
		return path
	}

	// Try different locations
	paths := []string{
		"config.yaml",
		"configs/config.yaml",
		"internal/config/config.yaml",
	}

	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return "config.yaml" // default
}

// overrideWithEnvVars recursively overrides config values with environment variables
func overrideWithEnvVars(config any) {
	//nolint:errcheck // Intentionally ignoring godotenv.Load errors - file may not exist
	_ = godotenv.Load() // Load .env file, ignore errors if file doesn't exist

	overrideWithEnvVarsRecursive(reflect.ValueOf(config).Elem(), strings.ToUpper(constants.AppName))
}

func overrideWithEnvVarsRecursive(v reflect.Value, prefix string) {
	if !v.IsValid() || !v.CanSet() {
		return
	}

	//nolint:exhaustive // Only handling struct types intentionally
	switch v.Kind() {
	case reflect.Struct:
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			fieldType := v.Type().Field(i)

			// Get the yaml tag name, fallback to struct field name
			yamlTag := fieldType.Tag.Get("yaml")
			fieldName := strings.Split(yamlTag, ",")[0]
			if fieldName == "" || fieldName == "-" {
				fieldName = strings.ToLower(fieldType.Name)
			}

			// Build the environment variable name
			var envKey string
			if prefix == "" {
				envKey = strings.ToUpper(fieldName)
			} else {
				envKey = prefix + "__" + strings.ToUpper(fieldName)
			}

			// Handle nested structs
			if field.Kind() == reflect.Struct {
				overrideWithEnvVarsRecursive(field, envKey)
				continue
			}

			// Check if environment variable exists and override
			if envValue := os.Getenv(envKey); envValue != "" {
				setFieldValue(field, envValue)
			}
		}
	default:
	}
}

func setFieldValue(field reflect.Value, value string) {
	if !field.CanSet() {
		return
	}
	//nolint:exhaustive // Only handling specific types we need
	switch field.Kind() {
	case reflect.String:
		field.SetString(value)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if intVal, err := strconv.ParseInt(value, 10, 64); err == nil {
			field.SetInt(intVal)
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if uintVal, err := strconv.ParseUint(value, 10, 64); err == nil {
			field.SetUint(uintVal)
		}
	case reflect.Float32, reflect.Float64:
		if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
			field.SetFloat(floatVal)
		}
	case reflect.Bool:
		if boolVal, err := strconv.ParseBool(value); err == nil {
			field.SetBool(boolVal)
		}
	case reflect.Slice:
		// Handle string slices (like AllowedOrigins)
		if field.Type().Elem().Kind() == reflect.String {
			// Split by comma and trim spaces
			parts := strings.Split(value, ",")
			slice := make([]string, len(parts))
			for i, part := range parts {
				slice[i] = strings.TrimSpace(part)
			}
			field.Set(reflect.ValueOf(slice))
		}
	}
}

// applyDefaultValues recursively applies default values from struct tags
func applyDefaultValues(v reflect.Value) {
	if !v.IsValid() || !v.CanSet() {
		return
	}

	//nolint:exhaustive // Only handling struct types intentionally
	switch v.Kind() {
	case reflect.Struct:
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			fieldType := v.Type().Field(i)

			// Handle nested structs
			if field.Kind() == reflect.Struct {
				applyDefaultValues(field)
				continue
			}

			// Get default value from struct tag
			if defaultValue := fieldType.Tag.Get("default"); defaultValue != "" {
				setFieldValue(field, defaultValue)
			}
		}
	default:
	}
}
