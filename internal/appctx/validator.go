package appctx

import (
	"telescope-be/internal/validation"

	"go.uber.org/zap"
)

// CustomValidator is a custom validator for Echo
type CustomValidator struct {
	Validator validation.Validator
}

// Validate validates the provided struct
func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.Validator.Struct(i)
}

// NewCustomValidator creates a new custom validator
func NewCustomValidator() *CustomValidator {
	// Create a logger for the validator
	logger, _ := zap.NewProduction()
	return &CustomValidator{
		Validator: validation.NewValidator(logger),
	}
}
