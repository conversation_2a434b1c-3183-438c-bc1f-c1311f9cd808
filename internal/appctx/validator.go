package appctx

import (
	validator "github.com/go-playground/validator/v10"
)

// CustomValidator is a custom validator for Echo
type CustomValidator struct {
	Validator *validator.Validate
}

// Validate validates the provided struct
func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.Validator.Struct(i)
}

// NewCustomValidator creates a new custom validator
func NewCustomValidator() *CustomValidator {
	return &CustomValidator{
		Validator: validator.New(),
	}
}
