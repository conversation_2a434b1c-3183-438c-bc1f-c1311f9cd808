// Package appctx provides application context utilities and configuration management.
package appctx

import echo "github.com/labstack/echo/v4"

type Response struct {
	Code    int      `json:"code"`
	Message string   `json:"message"`
	Data    any      `json:"data"`
	Error   string   `json:"error,omitempty"`
	Errors  []string `json:"errors,omitempty"`
	Meta    any      `json:"meta,omitempty"`
}

// ResponseBuilder provides a fluent interface for building Response objects
type ResponseBuilder struct {
	response *Response
}

// NewResponseBuilder creates a new ResponseBuilder instance
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{
		response: &Response{},
	}
}

// WithCode sets the response code
func (rb *ResponseBuilder) WithCode(code int) *ResponseBuilder {
	rb.response.Code = code
	return rb
}

// WithMessage sets the response message
func (rb *ResponseBuilder) WithMessage(message string) *ResponseBuilder {
	rb.response.Message = message
	return rb
}

// WithData sets the response data
func (rb *ResponseBuilder) WithData(data any) *ResponseBuilder {
	rb.response.Data = data
	return rb
}

// WithError sets a single error message
func (rb *ResponseBuilder) WithError(errMsg string) *ResponseBuilder {
	rb.response.Error = errMsg
	return rb
}

// WithErrors sets multiple error messages
func (rb *ResponseBuilder) WithErrors(errors []string) *ResponseBuilder {
	rb.response.Errors = errors
	return rb
}

// WithMeta sets the response metadata
func (rb *ResponseBuilder) WithMeta(meta any) *ResponseBuilder {
	rb.response.Meta = meta
	return rb
}

// Build returns the constructed Response
func (rb *ResponseBuilder) Build(c echo.Context) error {
	return c.JSON(rb.response.Code, rb.response)
}

// BuildResponse returns the constructed Response as a value (not pointer)
func (rb *ResponseBuilder) BuildResponse() Response {
	return *rb.response
}
