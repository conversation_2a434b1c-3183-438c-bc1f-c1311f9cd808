// Package audit provides audit logging functionality for tracking system events and user actions.
package audit

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"
)

// Operation constants
const (
	OpCreate       = "CREATE"
	OpUpdate       = "UPDATE"
	OpDelete       = "DELETE"
	OpStatusToggle = "STATUS_TOGGLE"
	OpRead         = "READ"
)

// Resource constants
const (
	ResourceCountry      = "country"
	ResourceCurrency     = "currency"
	ResourceCurrencyPair = "currency_pair"
	ResourceExchangeRate = "exchange_rate"
	ResourceUser         = "user"
)

// AuditEvent represents an audit log event
type AuditEvent struct {
	Timestamp  time.Time      `json:"timestamp"`
	UserID     int            `json:"user_id"`
	Operation  string         `json:"operation"`
	Resource   string         `json:"resource"`
	ResourceID string         `json:"resource_id,omitempty"`
	OldValues  map[string]any `json:"old_values,omitempty"`
	NewValues  map[string]any `json:"new_values,omitempty"`
	IPAddress  string         `json:"ip_address,omitempty"`
	UserAgent  string         `json:"user_agent,omitempty"`
	Success    bool           `json:"success"`
	Error      string         `json:"error,omitempty"`
}

// Logger provides audit logging functionality
type Logger interface {
	LogEvent(ctx context.Context, event AuditEvent)
}

// auditLogger implements the Logger interface
type auditLogger struct {
	logger *zap.Logger
}

// NewLogger creates a new audit logger
func NewLogger(logger *zap.Logger) Logger {
	return &auditLogger{
		logger: logger,
	}
}

// LogEvent logs a generic audit event
func (a *auditLogger) LogEvent(_ context.Context, event AuditEvent) {
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}

	eventJSON, err := json.Marshal(event)
	if err != nil {
		a.logger.Error("failed to marshal audit event",
			zap.Error(err),
			zap.String("operation", event.Operation),
			zap.String("resource", event.Resource),
		)
		return
	}

	if event.Success {
		a.logger.Info("audit_event",
			zap.String("event", string(eventJSON)),
			zap.String("operation", event.Operation),
			zap.String("resource", event.Resource),
		)
	} else {
		a.logger.Error("audit_event_failed",
			zap.String("event", string(eventJSON)),
			zap.String("operation", event.Operation),
			zap.String("resource", event.Resource),
			zap.String("error", event.Error),
		)
	}
}
