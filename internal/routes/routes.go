// Package routes provides HTTP route configuration and setup.
package routes

import (
	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/handler/http"
)

// Routes holds all route dependencies
type Routes struct {
	HTTPHandler *http.HTTPHandler
	Config      *appctx.Config
}

// NewRoutes creates a new routes instance with injected dependencies
func NewRoutes(handler *http.HTTPHandler, config *appctx.Config) *Routes {
	return &Routes{
		HTTPHandler: handler,
		Config:      config,
	}
}

// SetupRoutes configures all application routes
func (r *Routes) SetupRoutes(e *echo.Echo) {
	// Health check endpoint
	e.GET("/health", r.HTTPHandler.HealthHandler.HealthCheck)

	// API group
	api := e.Group("/api")

	// v1 group
	v1 := api.Group("/v1")

	r.SetupAuthRoutes(v1)
	r.SetupDuckRoutes(v1)
	r.SetupUserRoutes(v1)
	r.setupAPIDocsRoutes(e)
}

// SetupAuthRoutes configures authentication-related routes
func (r *Routes) SetupAuthRoutes(e *echo.Group) {
	authGroup := e.Group("/auth")
	authGroup.POST("/login", r.HTTPHandler.AuthHandler.Login)
	authGroup.POST("/refresh", r.HTTPHandler.AuthHandler.RefreshToken)
	authGroup.POST("/logout", r.HTTPHandler.AuthHandler.Logout)

	resetGroup := authGroup.Group("/reset")
	resetGroup.POST("", r.HTTPHandler.ResetHandler.CreateResetToken)
	resetGroup.POST("/verify", r.HTTPHandler.ResetHandler.VerifyResetToken)
}

// SetupDuckRoutes configures duck-related routes
func (r *Routes) SetupDuckRoutes(e *echo.Group) {
	duckGroup := e.Group("/duck")
	duckGroup.GET("", r.HTTPHandler.DuckHandler.GetDuck)
	duckGroup.POST("", r.HTTPHandler.DuckHandler.CreateDuck)
}

// SetupUserRoutes configures user-related routes
func (r *Routes) SetupUserRoutes(e *echo.Group) {
	userGroup := e.Group("/users")
	userGroup.POST("/register", r.HTTPHandler.UserHandler.RegisterUser)
}

func (r *Routes) setupAPIDocsRoutes(e *echo.Echo) {
	// Only setup API docs if enabled in configuration
	if !r.Config.Feature.APIDocs {
		return
	}

	// Serve static files (CSS, JS, images, etc.) for API docs
	e.Static("/static", "static")

	docsPath := "./api-docs"

	err := r.HTTPHandler.APIDocsHandler.SetupRoutes(e, docsPath)
	if err != nil {
		// now you can use your zap logger directly
		e.Logger.Error("failed to setup api docs: %s", err)
	}
}
