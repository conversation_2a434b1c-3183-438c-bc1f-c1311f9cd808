// Package metrics provides Prometheus monitoring and metrics collection.
package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// HTTP request metrics
	HTTPRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status_code"},
	)

	HTTPRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "Duration of HTTP requests in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	// Database operation metrics
	DatabaseOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "database_operations_total",
			Help: "Total number of database operations",
		},
		[]string{"operation", "table", "status"},
	)

	DatabaseOperationDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "database_operation_duration_seconds",
			Help:    "Duration of database operations in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"operation", "table"},
	)

	// Business metrics
	CountryOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "country_operations_total",
			Help: "Total number of country operations",
		},
		[]string{"operation", "status"},
	)

	CurrencyOperationsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "currency_operations_total",
			Help: "Total number of currency operations",
		},
		[]string{"operation", "status"},
	)

	ExchangeRateRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "exchange_rate_requests_total",
			Help: "Total number of exchange rate requests",
		},
		[]string{"from_currency", "to_currency", "status"},
	)
)
