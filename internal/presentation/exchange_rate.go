package presentation

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type ExchangeRateResponse struct {
	ID           uuid.UUID        `json:"id"`
	FromCurrency CurrencyResponse `json:"from_currency"`
	ToCurrency   CurrencyResponse `json:"to_currency"`
	Rate         decimal.Decimal  `json:"rate"`
	Source       string           `json:"source"`
	ValidFrom    time.Time        `json:"valid_from"`
	ValidUntil   *time.Time       `json:"valid_until"`
	CreatedAt    time.Time        `json:"created_at"`
}

type ExchangeRateListResponse struct {
	Rates []ExchangeRateResponse `json:"rates"`
	Total int                    `json:"total"`
}

type ExchangeRateHistoryResponse struct {
	Rates []ExchangeRateResponse `json:"rates"`
	Total int                    `json:"total"`
}
