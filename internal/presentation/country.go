package presentation

import (
	"time"

	"github.com/google/uuid"
)

type CountryResponse struct {
	ID                uuid.UUID `json:"id"`
	Code              string    `json:"code"`
	Name              string    `json:"name"`
	FlagEmoji         string    `json:"flag_emoji"`
	IsActive          bool      `json:"is_active"`
	SupportsSending   bool      `json:"supports_sending"`
	SupportsReceiving bool      `json:"supports_receiving"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

type CountryCreateRequest struct {
	Code              string `json:"code" validate:"required,len=3,alpha"`
	Name              string `json:"name" validate:"required,min=1,max=100"`
	FlagEmoji         string `json:"flag_emoji,omitempty" validate:"max=10"`
	IsActive          bool   `json:"is_active"`
	SupportsSending   bool   `json:"supports_sending"`
	SupportsReceiving bool   `json:"supports_receiving"`
}

type CountryUpdateRequest struct {
	Name              string `json:"name" validate:"required,min=1,max=100"`
	FlagEmoji         string `json:"flag_emoji,omitempty" validate:"max=10"`
	SupportsSending   bool   `json:"supports_sending"`
	SupportsReceiving bool   `json:"supports_receiving"`
}

type CountryToggleRequest struct {
	IsActive bool `json:"is_active"`
}

type CountryListResponse struct {
	Countries []CountryResponse `json:"countries"`
	Total     int               `json:"total"`
}
