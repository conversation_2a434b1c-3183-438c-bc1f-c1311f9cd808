// Package presentation defines the request and response structures for the application
package presentation

import (
	"time"

	"github.com/google/uuid"
)

// CreateResetTokenRequest represents the request payload for creating a reset token
type CreateResetTokenRequest struct {
	Email string `json:"email"`
}

// VerifyResetTokenRequest represents the request payload for verifying a reset token
type VerifyResetTokenRequest struct {
	ResetID  string `json:"reset_id"`
	Token    string `json:"token"`
	Password string `json:"password"`
}

// ResetTokenResponse represents the reset token response structure
type ResetTokenResponse struct {
	SendTo    string     `json:"send_to" url:"-"`
	ResetID   uuid.UUID  `json:"reset_id" url:"reset_id"`
	Token     string     `json:"token" url:"token"`
	CreatedAt time.Time  `json:"created_at" url:"-"`
	ExpiresAt time.Time  `json:"expires_at" url:"-"`
	UsedAt    *time.Time `json:"used_at,omitempty" url:"-"`
	ResetLink *string    `json:"reset_link,omitempty" url:"-"`
}
