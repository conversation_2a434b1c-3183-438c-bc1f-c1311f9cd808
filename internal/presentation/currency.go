package presentation

import (
	"time"

	"github.com/google/uuid"
)

type CurrencyResponse struct {
	ID            uuid.UUID        `json:"id"`
	Code          string           `json:"code"`
	Name          string           `json:"name"`
	Symbol        string           `json:"symbol"`
	DecimalPlaces int              `json:"decimal_places"`
	Country       *CountryResponse `json:"country,omitempty"`
	IsActive      bool             `json:"is_active"`
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
}

type CurrencyCreateRequest struct {
	Code          string     `json:"code" validate:"required,len=3,alpha"`
	Name          string     `json:"name" validate:"required,min=1,max=100"`
	Symbol        string     `json:"symbol" validate:"required,min=1,max=10"`
	DecimalPlaces int        `json:"decimal_places" validate:"min=0,max=8"`
	CountryID     *uuid.UUID `json:"country_id,omitempty"`
	IsActive      bool       `json:"is_active"`
}

type CurrencyUpdateRequest struct {
	Name          string `json:"name" validate:"required,min=1,max=100"`
	Symbol        string `json:"symbol" validate:"required,min=1,max=10"`
	DecimalPlaces int    `json:"decimal_places" validate:"min=0,max=8"`
	IsActive      bool   `json:"is_active"`
}

type CurrencyListResponse struct {
	Currencies []CurrencyResponse `json:"currencies"`
	Total      int                `json:"total"`
}
