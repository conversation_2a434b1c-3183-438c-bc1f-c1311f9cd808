package presentation

// HealthResponse represents the health check response structure
type HealthResponse struct {
	Status    string            `json:"status"`
	Timestamp string            `json:"timestamp"`
	Uptime    string            `json:"uptime"`
	Database  DatabaseStatus    `json:"database"`
	Details   map[string]string `json:"details,omitempty"`
}

// DatabaseStatus represents database health information
type DatabaseStatus struct {
	Status          string `json:"status"`
	ConnectionsOpen int32  `json:"connections_open"`
	ConnectionsIdle int32  `json:"connections_idle"`
}
