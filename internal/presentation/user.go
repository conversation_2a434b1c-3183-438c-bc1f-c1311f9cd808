// Package presentation defines request and response models for HTTP endpoints.
package presentation

import "time"

type UserResponse struct {
	ID          string    `json:"id"`
	Email       string    `json:"email"`
	Username    string    `json:"username"`
	StatusValid string    `json:"status"`
	CountryID   int       `json:"country_id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type UserRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Username  string `json:"username" validate:"required,username"`
	Password  string `json:"password" validate:"required,min=8,max=255"`
	CountryID int    `json:"country_id" validate:"required"`
}

type UserRegisterResponse struct {
	ID        string    `json:"id"` // always use external_id on the presentation layer
	Email     string    `json:"email"`
	Username  string    `json:"username"`
	CountryID int       `json:"country_id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
