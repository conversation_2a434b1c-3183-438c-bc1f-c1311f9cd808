package presentation

type DuckGetResponse struct {
	Name  string `json:"name"`
	Breed string `json:"breed"`
	Sound string `json:"sound"`
}

type DuckCreateRequest struct {
	Name  string `json:"name" validate:"required,min=1,max=100"`
	Breed string `json:"breed" validate:"required,min=1,max=50"`
	Sound string `json:"sound" validate:"required,min=1,max=50"`
}

type DuckCreateResponse struct {
	Name  string `json:"name"`
	Breed string `json:"breed"`
	Sound string `json:"sound"`
}
