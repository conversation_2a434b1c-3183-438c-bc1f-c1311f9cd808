package entity

import (
	"time"

	"github.com/google/uuid"
)

type Recipient struct {
	ID                int        `json:"id" db:"id"`
	ExternalID        uuid.UUID  `json:"external_id" db:"external_id"`
	FullName          string     `json:"full_name" db:"full_name"`
	BankName          string     `json:"bank_name" db:"bank_name"`
	BankAccountNumber string     `json:"bank_account_number" db:"bank_account_number"`
	BankID            int        `json:"bank_id" db:"bank_id"`
	UserID            int        `json:"user_id" db:"user_id"`
	DestinationUserID *int       `json:"destination_user_id" db:"destination_user_id"`
	CreatedAt         time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt         *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	Bank            *Bank `json:"bank,omitempty"`
	User            *User `json:"user,omitempty"`
	DestinationUser *User `json:"destination_user,omitempty"`
}
