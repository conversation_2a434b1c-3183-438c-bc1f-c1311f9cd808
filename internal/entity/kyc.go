package entity

import (
	"time"

	"github.com/google/uuid"
)

type KYC struct {
	ID             int        `json:"id" db:"id"`
	ExternalID     uuid.UUID  `json:"external_id" db:"external_id"`
	IdentityType   string     `json:"identity_type" db:"identity_type"`
	IdentityNumber string     `json:"identity_number" db:"identity_number"`
	Address        *string    `json:"address" db:"address"`
	Country        *string    `json:"country" db:"country"`
	State          *string    `json:"state" db:"state"`
	ZipNumber      *string    `json:"zip_number" db:"zip_number"`
	Provider       *string    `json:"provider" db:"provider"`
	Status         *string    `json:"status" db:"status"`
	Reason         *string    `json:"reason" db:"reason"`
	UserID         int        `json:"user_id" db:"user_id"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt      *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	User *User `json:"user,omitempty"`
}
