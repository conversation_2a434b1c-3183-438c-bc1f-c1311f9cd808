package entity

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type Transaction struct {
	ID          int             `json:"id" db:"id"`
	ExternalID  uuid.UUID       `json:"external_id" db:"external_id"`
	TotalAmount decimal.Decimal `json:"total_amount" db:"total_amount"`
	Currency    string          `json:"currency" db:"currency"`
	UserID      int             `json:"user_id" db:"user_id"`
	BankID      *int            `json:"bank_id" db:"bank_id"`
	RecipientID *int            `json:"recipient_id" db:"recipient_id"`
	Status      string          `json:"status" db:"status"`
	CreatedAt   time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at" db:"updated_at"`
	DeletedAt   *time.Time      `json:"deleted_at" db:"deleted_at"`

	// Relations
	User      *User      `json:"user,omitempty"`
	Bank      *Bank      `json:"bank,omitempty"`
	Recipient *Recipient `json:"recipient,omitempty"`
}
