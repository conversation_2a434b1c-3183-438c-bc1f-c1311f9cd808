package entity

import (
	"time"

	"github.com/google/uuid"
)

type BankAccount struct {
	ID            int        `json:"id" db:"id"`
	ExternalID    uuid.UUID  `json:"external_id" db:"external_id"`
	BankName      string     `json:"bank_name" db:"bank_name"`
	AccountNumber string     `json:"account_number" db:"account_number"`
	FullName      string     `json:"full_name" db:"full_name"`
	UserID        int        `json:"user_id" db:"user_id"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt     *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	User *User `json:"user,omitempty"`
}
