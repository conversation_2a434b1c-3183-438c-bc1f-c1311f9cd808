// Package entity defines data models and structures for the application.
package entity

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	ID           int        `json:"id" db:"id"`
	ExternalID   uuid.UUID  `json:"external_id" db:"external_id"`
	FirstName    string     `json:"first_name" db:"first_name"`
	LastName     string     `json:"last_name" db:"last_name"`
	Username     string     `json:"username" db:"username"`
	Email        string     `json:"email" db:"email"`
	PasswordHash string     `json:"password_hash" db:"password_hash"`
	PhoneNumber  *string    `json:"phone_number" db:"phone_number"`
	Status       *string    `json:"status" db:"status"`
	CountryID    *int       `json:"country_id" db:"country_id"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt    *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	Country *Country `json:"country,omitempty"`
}
