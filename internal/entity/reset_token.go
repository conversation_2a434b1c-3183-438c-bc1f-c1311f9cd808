package entity

import (
	"time"

	"github.com/google/uuid"
)

// ResetToken represents a password reset token entity
type ResetToken struct {
	ID         int        `db:"id"`
	ExternalID uuid.UUID  `db:"external_id"`
	UserID     int        `db:"user_id"`
	Token      string     `db:"token"`
	CreatedAt  time.Time  `db:"created_at"`
	ExpiresAt  time.Time  `db:"expires_at"`
	UsedAt     *time.Time `db:"used_at"`
}
