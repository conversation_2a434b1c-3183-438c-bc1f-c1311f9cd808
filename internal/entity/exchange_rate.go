package entity

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type ExchangeRate struct {
	ID             int                    `json:"id" db:"id"`
	ExternalID     uuid.UUID              `json:"external_id" db:"external_id"`
	CurrencyPairID int                    `json:"currency_pair_id" db:"currency_pair_id"`
	Rate           decimal.Decimal        `json:"rate" db:"rate"`
	Source         string                 `json:"source" db:"source"`
	SourceMetadata map[string]interface{} `json:"source_metadata" db:"source_metadata"`
	ValidFrom      time.Time              `json:"valid_from" db:"valid_from"`
	ValidUntil     *time.Time             `json:"valid_until" db:"valid_until"`
	IsActive       bool                   `json:"is_active" db:"is_active"`
	CreatedAt      time.Time              `json:"created_at" db:"created_at"`
	CreatedBy      *int                   `json:"created_by" db:"created_by"`
	Version        int                    `json:"version" db:"version"`

	// Relations
	CurrencyPair *CurrencyPair `json:"currency_pair,omitempty"`
}
