// Package entity defines data models and structures for the application.
package entity

import (
	"time"

	"github.com/google/uuid"
)

type FeatureFlag struct {
	ID          int        `json:"id" db:"id"`
	ExternalID  uuid.UUID  `json:"external_id" db:"external_id"`
	Name        string     `json:"name" db:"name"`
	Description *string    `json:"description" db:"description"`
	IsEnabled   bool       `json:"is_enabled" db:"is_enabled"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at" db:"deleted_at"`
}
