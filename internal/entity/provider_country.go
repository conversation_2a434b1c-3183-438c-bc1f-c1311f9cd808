package entity

import (
	"time"

	"github.com/google/uuid"
)

type ProviderCountry struct {
	ID         int        `json:"id" db:"id"`
	ExternalID uuid.UUID  `json:"external_id" db:"external_id"`
	ProviderID int        `json:"provider_id" db:"provider_id"`
	CountryID  int        `json:"country_id" db:"country_id"`
	Active     bool       `json:"active" db:"active"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	Provider *Provider `json:"provider,omitempty"`
	Country  *Country  `json:"country,omitempty"`
}
