package entity

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type CurrencyPair struct {
	ID                  int              `json:"id" db:"id"`
	ExternalID          uuid.UUID        `json:"external_id" db:"external_id"`
	FromCurrencyID      int              `json:"from_currency_id" db:"from_currency_id"`
	ToCurrencyID        int              `json:"to_currency_id" db:"to_currency_id"`
	IsActive            bool             `json:"is_active" db:"is_active"`
	MinAmount           decimal.Decimal  `json:"min_amount" db:"min_amount"`
	MaxAmount           *decimal.Decimal `json:"max_amount" db:"max_amount"`
	DeliveryTimeMin     int              `json:"delivery_time_min" db:"delivery_time_min"`
	DeliveryTimeMax     int              `json:"delivery_time_max" db:"delivery_time_max"`
	DeliveryDescription *string          `json:"delivery_description" db:"delivery_description"`
	CreatedAt           time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time        `json:"updated_at" db:"updated_at"`
	CreatedBy           *int             `json:"created_by" db:"created_by"`
	UpdatedBy           *int             `json:"updated_by" db:"updated_by"`
	Version             int              `json:"version" db:"version"`

	// Relations
	FromCurrency *Currency `json:"from_currency,omitempty"`
	ToCurrency   *Currency `json:"to_currency,omitempty"`
}
