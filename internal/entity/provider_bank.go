package entity

import (
	"time"

	"github.com/google/uuid"
)

type ProviderBank struct {
	ID           int        `json:"id" db:"id"`
	ExternalID   uuid.UUID  `json:"external_id" db:"external_id"`
	ProviderID   int        `json:"provider_id" db:"provider_id"`
	BankID       int        `json:"bank_id" db:"bank_id"`
	InternalCode *string    `json:"internal_code" db:"internal_code"`
	ExternalCode *string    `json:"external_code" db:"external_code"`
	Active       bool       `json:"active" db:"active"`
	CreatedAt    time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt    *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	Provider *Provider `json:"provider,omitempty"`
	Bank     *Bank     `json:"bank,omitempty"`
}
