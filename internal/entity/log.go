package entity

import (
	"time"

	"github.com/google/uuid"
)

type Log struct {
	ID            int        `json:"id" db:"id"`
	ExternalID    uuid.UUID  `json:"external_id" db:"external_id"`
	Status        *string    `json:"status" db:"status"`
	Request       *string    `json:"request" db:"request"`
	Response      *string    `json:"response" db:"response"`
	Error         *string    `json:"error" db:"error"`
	Event         *string    `json:"event" db:"event"`
	Description   *string    `json:"description" db:"description"`
	TransactionID int        `json:"transaction_id" db:"transaction_id"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt     *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	Transaction *Transaction `json:"transaction,omitempty"`
}
