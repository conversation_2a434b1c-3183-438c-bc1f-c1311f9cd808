package entity

import (
	"time"

	"github.com/google/uuid"
)

type RefreshToken struct {
	ID         int        `json:"id" db:"id"`
	ExternalID uuid.UUID  `json:"external_id" db:"external_id"`
	UserID     int        `json:"user_id" db:"user_id"`
	TokenHash  string     `json:"token_hash" db:"token_hash"`
	ExpiresAt  time.Time  `json:"expires_at" db:"expires_at"`
	IsRevoked  bool       `json:"is_revoked" db:"is_revoked"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at" db:"deleted_at"`

	// Relations
	User *User `json:"user,omitempty"`
}
