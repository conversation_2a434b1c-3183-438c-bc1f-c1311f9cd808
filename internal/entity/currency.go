package entity

import (
	"time"

	"github.com/google/uuid"
)

type Currency struct {
	ID            int       `json:"id" db:"id"`
	ExternalID    uuid.UUID `json:"external_id" db:"external_id"`
	Code          string    `json:"code" db:"code"`
	Name          string    `json:"name" db:"name"`
	Symbol        string    `json:"symbol" db:"symbol"`
	DecimalPlaces int       `json:"decimal_places" db:"decimal_places"`
	CountryID     *int      `json:"country_id" db:"country_id"`
	IsActive      bool      `json:"is_active" db:"is_active"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy     *int      `json:"created_by" db:"created_by"`
	UpdatedBy     *int      `json:"updated_by" db:"updated_by"`
	Version       int       `json:"version" db:"version"`

	// Relations
	Country *Country `json:"country,omitempty"`
}
