package entity

import (
	"time"

	"github.com/google/uuid"
)

type Country struct {
	ID                int       `json:"id" db:"id"`
	ExternalID        uuid.UUID `json:"external_id" db:"external_id"`
	Code              string    `json:"code" db:"code"`
	Name              string    `json:"name" db:"name"`
	FlagEmoji         string    `json:"flag_emoji" db:"flag_emoji"`
	IsActive          bool      `json:"is_active" db:"is_active"`
	SupportsSending   bool      `json:"supports_sending" db:"supports_sending"`
	SupportsReceiving bool      `json:"supports_receiving" db:"supports_receiving"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
	UpdatedAt         time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy         *int      `json:"created_by" db:"created_by"`
	UpdatedBy         *int      `json:"updated_by" db:"updated_by"`
	Version           int       `json:"version" db:"version"`
}
