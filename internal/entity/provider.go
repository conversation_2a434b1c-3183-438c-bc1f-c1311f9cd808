package entity

import (
	"time"

	"github.com/google/uuid"
)

type Provider struct {
	ID         int        `json:"id" db:"id"`
	ExternalID uuid.UUID  `json:"external_id" db:"external_id"`
	Name       string     `json:"name" db:"name"`
	Active     bool       `json:"active" db:"active"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt  *time.Time `json:"deleted_at" db:"deleted_at"`
}
