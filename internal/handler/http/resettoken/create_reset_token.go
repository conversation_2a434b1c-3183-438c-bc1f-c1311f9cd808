package resettoken

import (
	"net/http"

	echo "github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

// CreateResetToken handles the request to generate a reset token for a user.
func (h *ResetTokenHandler) CreateResetToken(c echo.Context) error {
	var req presentation.CreateResetTokenRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("failed to bind create reset token request", zap.Error(err))
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("INVALID_REQUEST").
			Build(c)
	}

	if err := c.Validate(req); err != nil {
		h.logger.Error("failed to validate create reset token request", zap.Error(err))
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("INVALID_REQUEST").
			Build(c)
	}

	if err := h.service.CreateResetToken(c.Request().Context(), req); err != nil {
		h.logger.Error("failed to create reset token", zap.Error(err))
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Unable to process reset token request").
			WithError("RESET_TOKEN_CREATE_FAILED").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusAccepted).
		WithMessage("If the account exists, a reset email has been sent").
		Build(c)
}
