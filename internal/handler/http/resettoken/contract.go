// Package resettoken provides HTTP handlers for reset token related endpoints.
package resettoken

import (
	"go.uber.org/zap"

	service "telescope-be/internal/service/resettoken"
)

type ResetTokenHandler struct {
	service service.Service
	logger  *zap.Logger
}

// NewResetTokenHandler builds a ResetTokenHandler with injected dependencies.
func NewResetTokenHandler(service service.Service, logger *zap.Logger) *ResetTokenHandler {
	return &ResetTokenHandler{
		service: service,
		logger:  logger,
	}
}
