package resettoken

import (
	"net/http"

	echo "github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

// VerifyResetToken handles validating a reset token and updating the user password.
func (h *ResetTokenHandler) VerifyResetToken(c echo.Context) error {
	var req presentation.VerifyResetTokenRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("failed to bind verify reset token request", zap.Error(err))
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("INVALID_REQUEST").
			Build(c)
	}

	if err := c.Validate(req); err != nil {
		h.logger.Error("failed to validate verify reset token request", zap.Error(err))
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("INVALID_REQUEST").
			Build(c)
	}

	resp, err := h.service.VerifyResetToken(c.Request().Context(), req)
	if err != nil {
		h.logger.Error("failed to verify reset token", zap.Error(err))

		status := http.StatusInternalServerError
		message := "Unable to verify reset token"
		code := "RESET_TOKEN_VERIFY_FAILED"
		if err.Error() == "reset token is invalid" || err.Error() == "token not found" {
			status = http.StatusBadRequest
			message = "Invalid or expired reset token"
			code = "RESET_TOKEN_INVALID"
		}

		return appctx.NewResponseBuilder().
			WithCode(status).
			WithMessage(message).
			WithError(code).
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Reset token verified").
		WithData(resp).
		Build(c)
}
