package duck

import (
	"net/http"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
)

func (h *<PERSON><PERSON><PERSON><PERSON>) GetDuck(c echo.Context) error {
	response, err := h.service.GetDuck()
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to get duck").
			WithError("Failed to get duck").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Duck retrieved successfully").
		WithData(response).
		Build(c)
}
