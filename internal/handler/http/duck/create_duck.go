// Package duck provides HTTP handlers for duck-related endpoints.
package duck

import (
	"net/http"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

func (h *<PERSON>H<PERSON><PERSON>) CreateDuck(c echo.Context) error {
	var req presentation.DuckCreateRequest
	if err := c.Bind(&req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Invalid request body").
			Build(c)
	}

	if err := c.Validate(req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Invalid request body").
			Build(c)
	}

	response, err := h.service.CreateDuck(req)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to create duck").
			WithError("Failed to create duck").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusCreated).
		WithMessage("Duck created successfully").
		WithData(response).
		Build(c)
}
