package auth

import (
	echo "github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"telescope-be/internal/service/auth"
)

type AuthHandler interface {
	Login(c echo.Context) error
	RefreshToken(c echo.Context) error
	Logout(c echo.Context) error
}

type authHandler struct {
	authService auth.AuthService
	logger      *zap.Logger
}

func NewAuthHandler(authService auth.AuthService, logger *zap.Logger) AuthHandler {
	return &authHandler{
		authService: authService,
		logger:      logger,
	}
}
