// Package auth provides HTTP handlers for authentication endpoints.
package auth

import (
	"fmt"
	"net/http"

	echo "github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

// handleBindAndValidate handles common request binding and validation
func (h *authHandler) handleBindAndValidate(c echo.Context, request interface{}, operation string) error {
	if err := c.Bind(request); err != nil {
		h.logger.Error(fmt.Sprintf("failed to bind %s request", operation), zap.Error(err))
		return c.JSON(http.StatusBadRequest, appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("INVALID_REQUEST").BuildResponse())
	}

	if err := c.Validate(request); err != nil {
		h.logger.Error(fmt.Sprintf("failed to validate %s request", operation), zap.Error(err))
		return c.<PERSON>(http.StatusBadRequest, appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("INVALID_REQUEST").BuildResponse())
	}

	return nil
}

// handleServiceError handles common service-level errors
func (h *authHandler) handleServiceError(c echo.Context, err error, operation string, errorCode string) error {
	h.logger.Error(fmt.Sprintf("%s failed", operation), zap.Error(err))
	return c.JSON(http.StatusUnauthorized, appctx.NewResponseBuilder().
		WithCode(http.StatusUnauthorized).
		WithMessage(fmt.Sprintf("%s failed", operation)).
		WithError(errorCode).BuildResponse())
}

func (h *authHandler) Login(c echo.Context) error {
	var request presentation.LoginRequest
	if err := h.handleBindAndValidate(c, &request, "login"); err != nil {
		return err
	}

	loginResponse, err := h.authService.Login(c.Request().Context(), request)
	if err != nil {
		return h.handleServiceError(c, err, "Login", "LOGIN_FAILED")
	}

	return c.JSON(http.StatusOK, loginResponse)
}

func (h *authHandler) RefreshToken(c echo.Context) error {
	var request presentation.RefreshTokenRequest
	if err := h.handleBindAndValidate(c, &request, "refresh token"); err != nil {
		return err
	}

	refreshResponse, err := h.authService.RefreshToken(c.Request().Context(), request)
	if err != nil {
		return h.handleServiceError(c, err, "Token refresh", "REFRESH_TOKEN_FAILED")
	}

	return c.JSON(http.StatusOK, refreshResponse)
}

func (h *authHandler) Logout(c echo.Context) error {
	var request presentation.RefreshTokenRequest
	if err := h.handleBindAndValidate(c, &request, "logout"); err != nil {
		return err
	}

	if err := h.authService.Logout(c.Request().Context(), request); err != nil {
		return h.handleServiceError(c, err, "Logout", "LOGOUT_FAILED")
	}

	return c.JSON(http.StatusOK, appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Successfully logged out").BuildResponse())
}
