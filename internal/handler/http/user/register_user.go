// Package user provides HTTP handlers for user-related endpoints.
package user

import (
	"fmt"
	"net/http"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

func (h *UserHandler) RegisterUser(c echo.Context) error {
	var request presentation.UserRequest
	if err := c.Bind(&request); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request format").
			WithError("Failed to parse request body").
			Build(c)
	}

	// Log the request for debugging
	fmt.Printf("Received register request: %+v\n", request)

	// Validate request
	if err := c.Validate(request); err != nil {
		fmt.Printf("Validation error: %v\n", err)
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Validation failed").
			WithError(err.Error()).
			Build(c)
	}

	user, err := h.service.RegisterUser(c.Request().Context(), request)
	if err != nil {
		if err.Error() == "account already exists" {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusConflict).
				WithMessage("Account already exists").
				WithError("A user with this email already exists").
				Build(c)
		}
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to register user").
			WithError(err.Error()).
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusCreated).
		WithMessage("User registered successfully").
		WithData(user).
		Build(c)
}
