// Package user provides HTTP handlers for user-related endpoints.
package user

import (
	echo "github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"telescope-be/internal/service/user"
)

type UserHandler struct {
	service user.UserService
	logger  *zap.Logger
	echo    *echo.Echo
}

// NewUserHandler creates a new user handler with injected service
func NewUserHandler(service user.UserService, logger *zap.Logger, e *echo.Echo) *UserHandler {
	return &UserHandler{
		service: service,
		logger:  logger,
		echo:    e,
	}
}
