// Package http provides HTTP handler interfaces and contracts.
package http

import (
	"go.uber.org/fx"

	"telescope-be/internal/handler/http/apidocs"
	"telescope-be/internal/handler/http/auth"
	"telescope-be/internal/handler/http/duck"
	"telescope-be/internal/handler/http/health"
	"telescope-be/internal/handler/http/resettoken"
	"telescope-be/internal/handler/http/user"
)

type (
	HTTPHandler struct {
		AuthHandler    auth.AuthHandler
		DuckHandler    *duck.DuckHandler
		HealthHandler  health.HealthHandler
		ResetHandler   *resettoken.ResetTokenHandler
		UserHandler    *user.UserHandler
		APIDocsHandler *apidocs.Handler
	}

	HTTPHandlerParams struct {
		fx.In
		AuthHandler    auth.AuthHandler
		DuckHandler    *duck.DuckHandler
		HealthHandler  health.HealthHandler
		ResetHandler   *resettoken.ResetTokenHandler
		UserHandler    *user.UserHandler
		APIDocsHandler *apidocs.Handler
	}
)

// NewHTTPHandler creates a new HTTP handler with injected dependencies
func NewHTTPHandler(
	param HTTPHandlerParams,
) *HTTPHandler {
	return &HTTPHandler{
		AuthHandler:    param.Au<PERSON><PERSON><PERSON><PERSON>,
		<PERSON>Handler:    param.DuckHandler,
		HealthHandler:  param.HealthHandler,
		ResetHandler:   param.ResetHandler,
		UserHandler:    param.UserHandler,
		APIDocsHandler: param.APIDocsHandler,
	}
}
