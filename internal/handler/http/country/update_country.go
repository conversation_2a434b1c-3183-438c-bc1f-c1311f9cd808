package country

import (
	"net/http"
	"strings"

	"github.com/google/uuid"
	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

func (h *CountryHandler) UpdateCountry(c echo.Context) error {
	externalIDStr := c.Param("id")
	externalID, err := uuid.Parse(externalIDStr)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid country ID").
			WithError("Invalid UUID format").
			Build(c)
	}

	var req presentation.CountryUpdateRequest

	if err := c.Bind(&req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Request binding failed").
			Build(c)
	}

	if err := c.<PERSON>(req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Request validation failed").
			WithError("Invalid request data").
			Build(c)
	}

	country, err := h.service.Update(c.Request().Context(), externalID, &req)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusNotFound).
				WithMessage("Country not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to update country").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Country updated successfully").
		WithData(country).
		Build(c)
}

func (h *CountryHandler) ToggleCountryStatus(c echo.Context) error {
	externalIDStr := c.Param("id")
	externalID, err := uuid.Parse(externalIDStr)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid country ID").
			WithError("Invalid UUID format").
			Build(c)
	}

	var req presentation.CountryToggleRequest

	if err := c.Bind(&req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Request binding failed").
			Build(c)
	}

	if err := c.Validate(req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Request validation failed").
			WithError("Invalid request data").
			Build(c)
	}

	err = h.service.ToggleStatus(c.Request().Context(), externalID, &req)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusNotFound).
				WithMessage("Country not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to toggle country status").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Country status updated successfully").
		Build(c)
}
