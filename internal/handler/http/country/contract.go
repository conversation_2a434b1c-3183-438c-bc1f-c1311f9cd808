// Package country provides HTTP handlers for country-related operations.
package country

import (
	"telescope-be/internal/service/country"
)

type CountryHandler struct {
	service country.CountryService
}

// NewCountryHandler creates a new country handler with injected service
func NewCountryHandler(service country.CountryService) *CountryHandler {
	return &CountryHandler{
		service: service,
	}
}
