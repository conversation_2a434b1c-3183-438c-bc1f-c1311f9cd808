package country

import (
	"net/http"
	"strings"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

func (h *CountryHandler) CreateCountry(c echo.Context) error {
	var req presentation.CountryCreateRequest

	if err := c.Bind(&req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Request binding failed").
			Build(c)
	}

	if err := c.Validate(req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Request validation failed").
			WithError("Invalid request data").
			Build(c)
	}

	country, err := h.service.Create(c.Request().Context(), &req)
	if err != nil {
		// Check for specific business errors
		switch {
		case strings.Contains(err.Error(), "already exists"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusConflict).
				WithMessage("Country already exists").
				WithError(err.Error()).
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to create country").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusCreated).
		WithMessage("Country created successfully").
		WithData(country).
		Build(c)
}
