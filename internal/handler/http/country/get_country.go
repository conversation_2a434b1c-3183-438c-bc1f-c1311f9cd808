package country

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/google/uuid"
	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
)

func (h *CountryHandler) GetCountry(c echo.Context) error {
	externalIDStr := c.Param("id")
	externalID, err := uuid.Parse(externalIDStr)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid country ID").
			WithError("Invalid UUID format").
			Build(c)
	}

	country, err := h.service.GetByID(c.Request().Context(), externalID)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusNotFound).
				WithMessage("Country not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to get country").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Country retrieved successfully").
		WithData(country).
		Build(c)
}

func (h *CountryHandler) ListCountries(c echo.Context) error {
	activeOnlyStr := c.QueryParam("active_only")
	activeOnly := false

	if activeOnlyStr != "" {
		var err error
		activeOnly, err = strconv.ParseBool(activeOnlyStr)
		if err != nil {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid active_only parameter").
				WithError("Must be true or false").
				Build(c)
		}
	}

	countries, err := h.service.List(c.Request().Context(), activeOnly)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to list countries").
			WithError("Internal server error").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Countries retrieved successfully").
		WithData(countries).
		Build(c)
}
