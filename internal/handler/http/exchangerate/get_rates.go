package exchangerate

import (
	"net/http"
	"strconv"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
)

func (h *ExchangeRateHandler) GetCurrentRate(c echo.Context) error {
	fromCurrency := c.QueryParam("from")
	toCurrency := c.QueryParam("to")

	if fromCurrency == "" || toCurrency == "" {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Missing required parameters").
			WithError("Both 'from' and 'to' currency parameters are required").
			Build(c)
	}

	rate, err := h.service.GetCurrentRate(c.Request().Context(), fromCurrency, toCurrency)
	if err != nil {
		switch {
		case containsString(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusNotFound).
				WithMessage("Exchange rate not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to get exchange rate").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Exchange rate retrieved successfully").
		WithData(rate).
		Build(c)
}

func (h *ExchangeRateHandler) GetCurrentRates(c echo.Context) error {
	activeOnlyStr := c.QueryParam("active_only")
	activeOnly := true // Default to active only

	if activeOnlyStr != "" {
		var err error
		activeOnly, err = strconv.ParseBool(activeOnlyStr)
		if err != nil {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid active_only parameter").
				WithError("Must be true or false").
				Build(c)
		}
	}

	rates, err := h.service.GetCurrentRates(c.Request().Context(), activeOnly)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to get current rates").
			WithError("Internal server error").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Current rates retrieved successfully").
		WithData(rates).
		Build(c)
}

func (h *ExchangeRateHandler) GetRateHistory(c echo.Context) error {
	fromCurrency := c.QueryParam("from")
	toCurrency := c.QueryParam("to")
	limitStr := c.QueryParam("limit")

	if fromCurrency == "" || toCurrency == "" {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Missing required parameters").
			WithError("Both 'from' and 'to' currency parameters are required").
			Build(c)
	}

	limit := 50 // Default limit
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil || limit <= 0 || limit > 100 {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid limit parameter").
				WithError("Limit must be a number between 1 and 100").
				Build(c)
		}
	}

	history, err := h.service.GetRateHistory(c.Request().Context(), fromCurrency, toCurrency, limit)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to get rate history").
			WithError("Internal server error").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Rate history retrieved successfully").
		WithData(history).
		Build(c)
}

func containsString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
