// Package apidocs provides HTTP handlers for serving API documentation from Markdown files.
// It supports syntax highlighting, GitHub Flavored Markdown, and responsive HTML output.
//
// NOTE: I'm too lazy to separate the business logic into separated package
package apidocs

import (
	"bytes"
	"html"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	chromahtml "github.com/alecthomas/chroma/v2/formatters/html"
	"github.com/yuin/goldmark"
	highlighting "github.com/yuin/goldmark-highlighting/v2"
	"github.com/yuin/goldmark/extension"
	"github.com/yuin/goldmark/parser"
	htmlrenderer "github.com/yuin/goldmark/renderer/html"
)

const (
	markdownExt       = ".md"
	apiDocsRoute      = "/api-docs"
	staticCSSPath     = "/static/style.css"
	markdownBodyClass = "markdown-body"
)

// Handler provides HTTP handlers for serving API documentation from Markdown files.
// It converts Markdown files to HTML with syntax highlighting and serves them via HTTP routes.
type Handler struct {
	logger  *zap.Logger
	echo    *echo.Echo
	baseDir string
}

// NewAPIDocsHandler creates a new API documentation handler with the provided logger and Echo instance.
func NewAPIDocsHandler(logger *zap.Logger, e *echo.Echo) *Handler {
	return &Handler{
		logger: logger,
		echo:   e,
	}
}

// ServeRecursive returns a handler that serves an index page listing all available markdown documentation files.
func (h *Handler) ServeRecursive(dir string) echo.HandlerFunc {
	return func(c echo.Context) error {
		links, err := h.collectMarkdownLinks(dir)
		if err != nil {
			h.logger.Error("Failed to collect markdown links",
				zap.String("directory", dir),
				zap.Error(err))
			return c.String(http.StatusInternalServerError, "Failed to load documentation index")
		}

		htmlContent := h.buildIndexHTML(links)
		return c.HTML(http.StatusOK, htmlContent)
	}
}

// collectMarkdownLinks walks through the directory and collects HTML links to all markdown files.
func (h *Handler) collectMarkdownLinks(dir string) ([]string, error) {
	var links []string

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}

		if filepath.Ext(path) == markdownExt {
			route := h.createRouteFromPath(path, dir)
			escapedRoute := html.EscapeString(route)
			link := `<li><a href="` + escapedRoute + `">` + escapedRoute + `</a></li>`
			links = append(links, link)
		}
		return nil
	})

	return links, err
}

// buildIndexHTML constructs the HTML for the documentation index page with proper structure and styling.
func (h *Handler) buildIndexHTML(links []string) string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>API Documentation Index</title>
	<link rel="stylesheet" href="` + staticCSSPath + `">
</head>
<body class="` + markdownBodyClass + `">
	<header>
		<h1>API Documentation</h1>
		<p>Browse the available documentation below:</p>
	</header>
	<main>
		<ul class="doc-index">
` + strings.Join(links, "\n\t\t\t") + `
		</ul>
	</main>
</body>
</html>`
}

// validateFilePath validates and cleans a file path to prevent directory traversal attacks.
func (h *Handler) validateFilePath(path, baseDir string) (string, error) {
	// Clean the path to remove any relative path components
	cleanPath := filepath.Clean(path)

	// Get absolute path of the base directory
	absBaseDir, err := filepath.Abs(baseDir)
	if err != nil {
		h.logger.Error("Failed to resolve base directory absolute path",
			zap.String("baseDir", baseDir),
			zap.Error(err))
		return "", err
	}

	// Convert to absolute path if not already
	if !filepath.IsAbs(cleanPath) {
		absPath, err := filepath.Abs(cleanPath)
		if err != nil {
			h.logger.Error("Failed to resolve absolute path",
				zap.String("path", path),
				zap.Error(err))
			return "", err
		}
		cleanPath = absPath
	}

	// Ensure the path doesn't contain directory traversal attempts
	if strings.Contains(cleanPath, "..") {
		h.logger.Warn("Potential directory traversal attempt blocked",
			zap.String("path", path))
		return "", echo.NewHTTPError(http.StatusForbidden, "invalid file path")
	}

	// Ensure the file is within the allowed base directory
	relPath, err := filepath.Rel(absBaseDir, cleanPath)
	if err != nil || strings.HasPrefix(relPath, "..") {
		h.logger.Warn("File access outside base directory blocked",
			zap.String("path", path),
			zap.String("baseDir", baseDir))
		return "", echo.NewHTTPError(http.StatusForbidden, "file access denied")
	}

	return cleanPath, nil
}

// renderMarkdownFile converts a Markdown file into HTML with syntax highlighting and full extension support.
func (h *Handler) renderMarkdownFile(path string) (string, error) {
	cleanPath, err := h.validateFilePath(path, h.baseDir)
	if err != nil {
		return "", err
	}

	input, err := os.ReadFile(cleanPath) //nolint:gosec // Path is validated by validateFilePath function
	if err != nil {
		h.logger.Error("Failed to read markdown file",
			zap.String("path", cleanPath),
			zap.Error(err))
		return "", err
	}

	md := h.createMarkdownParser()

	var buf bytes.Buffer
	if err := md.Convert(input, &buf); err != nil {
		h.logger.Error("Failed to convert markdown to HTML",
			zap.String("path", path),
			zap.Error(err))
		return "", err
	}

	return buf.String(), nil
}

// createMarkdownParser creates a configured Goldmark parser with all extensions and optimal settings.
func (h *Handler) createMarkdownParser() goldmark.Markdown {
	return goldmark.New(
		goldmark.WithExtensions(
			extension.GFM,
			extension.Table,
			extension.Strikethrough,
			extension.Linkify,
			extension.TaskList,
			extension.DefinitionList,
			extension.Footnote,
			extension.Typographer,
			h.createSyntaxHighlighter(),
		),
		goldmark.WithParserOptions(
			parser.WithAutoHeadingID(),
			parser.WithAttribute(),
		),
		goldmark.WithRendererOptions(
			htmlrenderer.WithHardWraps(),
			htmlrenderer.WithXHTML(),
			htmlrenderer.WithUnsafe(),
		),
	)
}

// createSyntaxHighlighter creates the syntax highlighting extension with optimal settings for code readability.
func (h *Handler) createSyntaxHighlighter() goldmark.Extender {
	return highlighting.NewHighlighting(
		highlighting.WithStyle("catppuccin-mocha"),
		highlighting.WithFormatOptions(
			chromahtml.WithLineNumbers(true),
			chromahtml.WithAllClasses(true),
		),
		highlighting.WithGuessLanguage(true),
	)
}

// SetupRoutes configures all API documentation routes including the index and individual document routes.
func (h *Handler) SetupRoutes(e *echo.Echo, dir string) error {
	// Store the base directory for validation
	h.baseDir = dir

	// Register the main index route
	e.GET(apiDocsRoute, h.ServeRecursive(dir))

	// Register individual routes for each markdown file
	return h.registerMarkdownRoutes(e, dir)
}

// registerMarkdownRoutes walks through the directory and registers a route for each markdown file.
func (h *Handler) registerMarkdownRoutes(e *echo.Echo, dir string) error {
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if info.IsDir() {
			return nil
		}

		if filepath.Ext(path) == markdownExt {
			route := h.createRouteFromPath(path, dir)
			mdPath := path // Capture loop variable for closure

			e.GET(route, func(c echo.Context) error {
				return h.serveMarkdownFile(c, mdPath, route)
			})
		}
		return nil
	})
}

// createRouteFromPath converts a file system path to a clean URL route.
func (h *Handler) createRouteFromPath(path, dir string) string {
	return strings.TrimSuffix(strings.TrimPrefix(path, dir+"/"), markdownExt)
}

// serveMarkdownFile handles serving a single markdown file as a complete HTML page.
func (h *Handler) serveMarkdownFile(c echo.Context, mdPath, route string) error {
	content, err := h.renderMarkdownFile(mdPath)
	if err != nil {
		h.logger.Error("Failed to render markdown file for HTTP response",
			zap.String("file_path", mdPath),
			zap.String("route", route),
			zap.Error(err))
		return c.String(http.StatusInternalServerError, "Failed to render documentation")
	}

	htmlPage := h.buildDocumentHTML(route, content)
	return c.HTML(http.StatusOK, htmlPage)
}

// buildDocumentHTML constructs a complete HTML page for a documentation file with navigation and proper structure.
func (h *Handler) buildDocumentHTML(title, content string) string {
	escapedTitle := html.EscapeString(title)

	return `<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>` + escapedTitle + ` - API Documentation</title>
	<link rel="stylesheet" href="` + staticCSSPath + `">
</head>
<body class="` + markdownBodyClass + `">
	<nav class="doc-nav">
		<a href="` + apiDocsRoute + `" class="back-link">&larr; Back to Documentation Index</a>
	</nav>
	<main class="doc-content">
		` + content + `
	</main>
	<footer class="doc-footer">
		<a href="` + apiDocsRoute + `" class="back-link">Return to Documentation Index</a>
	</footer>
</body>
</html>`
}
