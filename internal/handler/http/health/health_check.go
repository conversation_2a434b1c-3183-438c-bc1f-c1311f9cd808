package health

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	echo "github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"telescope-be/internal/presentation"
)

// healthHandler implements HealthHandler interface
type healthHandler struct {
	pool      *pgxpool.Pool
	logger    *zap.Logger
	startTime time.Time
}

// NewHealthHandler creates a new health handler instance
func NewHealthHandler(pool *pgxpool.Pool, logger *zap.Logger) HealthHandler {
	return &healthHandler{
		pool:      pool,
		logger:    logger,
		startTime: time.Now(),
	}
}

// HealthCheck handles GET /health endpoint
func (h *healthHandler) HealthCheck(c echo.Context) error {
	ctx, cancel := context.WithTimeout(c.Request().Context(), 5*time.Second)
	defer cancel()

	// Calculate uptime
	uptime := time.Since(h.startTime)

	// Initialize response with default healthy status
	response := presentation.HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Uptime:    formatUptime(uptime),
		Database: presentation.DatabaseStatus{
			Status: "unknown",
		},
	}

	// Check database connection
	dbStatus, dbError := h.checkDatabaseHealth(ctx)
	response.Database = dbStatus

	// Determine overall health status
	httpStatus := http.StatusOK
	if dbError != nil {
		response.Status = "unhealthy"
		response.Details = map[string]string{
			"database_error": dbError.Error(),
		}
		httpStatus = http.StatusInternalServerError
		h.logger.Error("Health check failed", zap.Error(dbError))
	} else {
		h.logger.Debug("Health check passed")
	}

	return c.JSON(httpStatus, response)
}

// checkDatabaseHealth performs database connectivity and status checks
func (h *healthHandler) checkDatabaseHealth(ctx context.Context) (presentation.DatabaseStatus, error) {
	status := presentation.DatabaseStatus{
		Status: "unhealthy",
	}

	if h.pool == nil {
		return status, echo.NewHTTPError(http.StatusInternalServerError, "database pool not initialized")
	}

	// Test database connectivity with ping
	if err := h.pool.Ping(ctx); err != nil {
		return status, err
	}

	// Get pool statistics
	stat := h.pool.Stat()
	status.ConnectionsOpen = stat.AcquiredConns()
	status.ConnectionsIdle = stat.IdleConns()
	status.Status = "healthy"

	return status, nil
}

// formatUptime formats duration into human-readable uptime string
func formatUptime(duration time.Duration) string {
	seconds := int(duration.Seconds())
	days := seconds / (24 * 3600)
	seconds %= (24 * 3600)
	hours := seconds / 3600
	seconds %= 3600
	minutes := seconds / 60
	seconds %= 60

	switch {
	case days > 0:
		return fmt.Sprintf("%dd %dh %dm %ds", days, hours, minutes, seconds)
	case hours > 0:
		return fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds)
	case minutes > 0:
		return fmt.Sprintf("%dm %ds", minutes, seconds)
	default:
		return fmt.Sprintf("%ds", seconds)
	}
}
