package currency

import (
	"net/http"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

func (h *CurrencyHandler) CreateCurrency(c echo.Context) error {
	var req presentation.CurrencyCreateRequest

	if err := c.Bind(&req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Request binding failed").
			Build(c)
	}

	if err := c.Validate(req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Request validation failed").
			WithError("Invalid request data").
			Build(c)
	}

	currency, err := h.service.Create(c.Request().Context(), &req)
	if err != nil {
		switch {
		case containsString(err.Error(), "already exists"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusConflict).
				WithMessage("Currency already exists").
				WithError(err.Error()).
				Build(c)
		case containsString(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid country reference").
				WithError("Country not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to create currency").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusCreated).
		WithMessage("Currency created successfully").
		WithData(currency).
		Build(c)
}

func containsString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
