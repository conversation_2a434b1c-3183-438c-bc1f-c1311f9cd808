// Package currency provides HTTP handlers for currency-related operations.
package currency

import (
	"net/http"
	"strconv"

	"github.com/google/uuid"
	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
)

func (h *CurrencyHandler) GetCurrency(c echo.Context) error {
	externalIDStr := c.Param("id")
	externalID, err := uuid.Parse(externalIDStr)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid currency ID").
			WithError("Invalid UUID format").
			Build(c)
	}

	currency, err := h.service.GetByID(c.Request().Context(), externalID)
	if err != nil {
		switch {
		case containsString(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusNotFound).
				WithMessage("Currency not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to get currency").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Currency retrieved successfully").
		WithData(currency).
		Build(c)
}

func (h *CurrencyHandler) ListCurrencies(c echo.Context) error {
	// Default to active currencies only
	activeOnly := true
	if activeOnlyStr := c.QueryParam("active_only"); activeOnlyStr != "" {
		var err error
		activeOnly, err = strconv.ParseBool(activeOnlyStr)
		if err != nil {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid active_only parameter").
				WithError("Must be true or false").
				Build(c)
		}
	}

	// Parse pagination parameters
	offset := 0
	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		var err error
		offset, err = strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid offset parameter").
				WithError("Must be a non-negative integer").
				Build(c)
		}
	}

	limit := 50 // Default limit
	if limitStr := c.QueryParam("limit"); limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil || limit < 0 {
			return appctx.NewResponseBuilder().
				WithCode(http.StatusBadRequest).
				WithMessage("Invalid limit parameter").
				WithError("Must be a non-negative integer").
				Build(c)
		}
		if limit > 100 {
			limit = 100 // Maximum limit
		}
	}

	currencies, err := h.service.List(c.Request().Context(), activeOnly, offset, limit)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusInternalServerError).
			WithMessage("Failed to list currencies").
			WithError("Internal server error").
			Build(c)
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Currencies retrieved successfully").
		WithData(currencies).
		Build(c)
}
