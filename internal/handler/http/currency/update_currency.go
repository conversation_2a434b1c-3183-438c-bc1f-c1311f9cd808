package currency

import (
	"net/http"

	"github.com/google/uuid"
	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/presentation"
)

func (h *CurrencyHandler) UpdateCurrency(c echo.Context) error {
	externalIDStr := c.Param("id")
	externalID, err := uuid.Parse(externalIDStr)
	if err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid currency ID").
			WithError("Invalid UUID format").
			Build(c)
	}

	var req presentation.CurrencyUpdateRequest

	if err := c.Bind(&req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Invalid request body").
			WithError("Request binding failed").
			Build(c)
	}

	if err := c.<PERSON>(req); err != nil {
		return appctx.NewResponseBuilder().
			WithCode(http.StatusBadRequest).
			WithMessage("Request validation failed").
			WithError("Invalid request data").
			Build(c)
	}

	currency, err := h.service.Update(c.Request().Context(), externalID, &req)
	if err != nil {
		switch {
		case containsString(err.Error(), "not found"):
			return appctx.NewResponseBuilder().
				WithCode(http.StatusNotFound).
				WithMessage("Currency not found").
				Build(c)
		default:
			return appctx.NewResponseBuilder().
				WithCode(http.StatusInternalServerError).
				WithMessage("Failed to update currency").
				WithError("Internal server error").
				Build(c)
		}
	}

	return appctx.NewResponseBuilder().
		WithCode(http.StatusOK).
		WithMessage("Currency updated successfully").
		WithData(currency).
		Build(c)
}
