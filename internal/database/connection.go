// Package database provides database connection and configuration utilities.
package database

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
)

// NewDatabaseConnection creates a new database connection using DSN
func NewDatabaseConnection(config *appctx.Config, logger *zap.Logger) (*pgxpool.Pool, error) {
	dsn := config.Database.DSN
	if dsn == "" {
		return nil, fmt.Errorf("postgres DSN is required")
	}

	poolConfig, err := createPoolConfig(dsn, config.Database)
	if err != nil {
		return nil, err
	}

	pool, err := connectWithRetry(poolConfig, dsn, config.Database)
	if err != nil {
		return nil, err
	}

	if err := testConnection(pool); err != nil {
		pool.Close()
		return nil, err
	}

	logger.Info("Database connection established successfully")
	return pool, nil
}

// createPoolConfig creates and configures a pgx pool configuration
func createPoolConfig(dsn string, dbConfig appctx.DatabaseConfig) (*pgxpool.Config, error) {
	poolConfig, err := pgxpool.ParseConfig(dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	applyPoolSettings(poolConfig, dbConfig)
	return poolConfig, nil
}

// applyPoolSettings applies database configuration to pool config
func applyPoolSettings(poolConfig *pgxpool.Config, dbConfig appctx.DatabaseConfig) {
	if dbConfig.MaxOpenConns > 0 {
		poolConfig.MaxConns = dbConfig.MaxOpenConns
	}
	if dbConfig.MaxIdleConns > 0 {
		poolConfig.MinConns = dbConfig.MaxIdleConns
	}

	if dbConfig.ConnMaxLifetime != "" {
		if lifetime, err := time.ParseDuration(dbConfig.ConnMaxLifetime); err == nil {
			poolConfig.MaxConnLifetime = lifetime
		}
	}
}

// connectWithRetry attempts to connect with fallback to disabled SSL for local connections
func connectWithRetry(poolConfig *pgxpool.Config, dsn string, dbConfig appctx.DatabaseConfig) (*pgxpool.Pool, error) {
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err == nil {
		return pool, nil
	}

	// Try with SSL disabled for local development
	if !isLocalConnection(dsn) {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return tryWithDisabledSSL(dsn, dbConfig)
}

// tryWithDisabledSSL attempts connection with SSL disabled for local development
func tryWithDisabledSSL(dsn string, dbConfig appctx.DatabaseConfig) (*pgxpool.Pool, error) {
	modifiedDSN := modifySSLMode(dsn, "disable")
	modifiedConfig, err := pgxpool.ParseConfig(modifiedDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to parse modified database config: %w", err)
	}

	applyPoolSettings(modifiedConfig, dbConfig)

	pool, err := pgxpool.NewWithConfig(context.Background(), modifiedConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database (both SSL modes tried): %w", err)
	}

	return pool, nil
}

// testConnection tests the database connection with a ping
func testConnection(pool *pgxpool.Pool) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	return nil
}

// isLocalConnection checks if the DSN points to a local database
func isLocalConnection(dsn string) bool {
	// Parse the DSN to check if it's a local connection
	if strings.Contains(dsn, "localhost") || strings.Contains(dsn, "127.0.0.1") {
		return true
	}

	// Try to parse as URL to check host
	if u, err := url.Parse(dsn); err == nil && u.Host != "" {
		host := u.Hostname()
		return host == "localhost" || host == "127.0.0.1"
	}

	return false
}

// modifySSLMode modifies the SSL mode in a DSN string
func modifySSLMode(dsn, sslMode string) string {
	// If DSN is a URL format
	if strings.HasPrefix(dsn, "postgres://") || strings.HasPrefix(dsn, "postgresql://") {
		u, err := url.Parse(dsn)
		if err != nil {
			return dsn // Return original if parsing fails
		}

		q := u.Query()
		q.Set("sslmode", sslMode)
		u.RawQuery = q.Encode()
		return u.String()
	}

	// If DSN is key=value format
	if strings.Contains(dsn, "sslmode=") {
		// Replace existing sslmode
		parts := strings.Split(dsn, " ")
		for i, part := range parts {
			if strings.HasPrefix(part, "sslmode=") {
				parts[i] = fmt.Sprintf("sslmode=%s", sslMode)
				break
			}
		}
		return strings.Join(parts, " ")
	}

	// Add sslmode if not present
	return fmt.Sprintf("%s sslmode=%s", dsn, sslMode)
}
