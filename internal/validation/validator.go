// Package validation provides a centralized validation service for the application.
// It wraps go-playground/validator with dependency injection support and pre-registered
// common validators for consistent validation across all services.
package validation

import (
	"fmt"
	"regexp"
	"strings"

	validator "github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

const (
	// Username validation constraints
	MinUsernameLength = 3
	MaxUsernameLength = 255
)

var (
	// usernameRegex defines the allowed pattern for usernames
	// Allows alphanumeric characters, underscores, hyphens, and dots
	// Must start and end with alphanumeric characters
	// Cannot have consecutive special characters
	usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$`)

	// emailRegex provides basic email validation
	// This is a simplified regex for basic validation
	// More comprehensive validation should use go-playground/validator
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
)

// ValidationError represents a detailed validation error
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

func (e ValidationError) Error() string {
	return e.Message
}

// ValidationErrors represents multiple validation errors
type ValidationErrors []ValidationError

func (e ValidationErrors) Error() string {
	if len(e) == 0 {
		return ""
	}
	if len(e) == 1 {
		return e[0].Error()
	}
	return fmt.Sprintf("validation failed: %d errors", len(e))
}

// Validator provides validation operations for structs and fields
type Validator interface {
	// Struct validates a struct using validation tags
	Struct(s any) error
	// StructWithDetails validates a struct and returns detailed error information
	StructWithDetails(s any) ValidationErrors
	// Field validates a single field with a validation tag
	Field(field any, tag string) error
	// FieldWithDetails validates a field and returns detailed error information
	FieldWithDetails(field any, tag string, fieldName string) *ValidationError
	// RegisterCustomValidator registers a new custom validation function
	RegisterCustomValidator(tag string, fn validator.Func) error
	// IsValidUsername validates if a string is a valid username
	IsValidUsername(username string) bool
	// IsValidEmail validates if a string is a valid email address
	IsValidEmail(email string) bool
	// IsEmailOrUsername determines if an identifier is an email or username
	IsEmailOrUsername(identifier string) string
	// SanitizeIdentifier cleans and normalizes an identifier
	SanitizeIdentifier(identifier string) string
}

// validatorService implements the Validator interface
type validatorService struct {
	validate *validator.Validate
	logger   *zap.Logger
}

// NewValidator creates a new validator service with pre-registered common validators
func NewValidator(logger *zap.Logger) Validator {
	v := validator.New(validator.WithRequiredStructEnabled())

	service := &validatorService{
		validate: v,
		logger:   logger,
	}
	service.registerCommonValidators()

	return service
}

// Struct validates a struct using validation tags
func (v *validatorService) Struct(s any) error {
	return v.validate.Struct(s)
}

// Field validates a single field with a validation tag
func (v *validatorService) Field(field any, tag string) error {
	return v.validate.Var(field, tag)
}

// StructWithDetails validates a struct and returns detailed error information
func (v *validatorService) StructWithDetails(s any) ValidationErrors {
	err := v.validate.Struct(s)
	if err == nil {
		return nil
	}

	var validationErrors ValidationErrors
	if validatorErrors, ok := err.(validator.ValidationErrors); ok {
		for _, fieldError := range validatorErrors {
			validationErrors = append(validationErrors, ValidationError{
				Field:   fieldError.Field(),
				Tag:     fieldError.Tag(),
				Value:   fmt.Sprintf("%v", fieldError.Value()),
				Message: v.formatErrorMessage(fieldError),
			})
		}
	}
	return validationErrors
}

// FieldWithDetails validates a field and returns detailed error information
func (v *validatorService) FieldWithDetails(field any, tag string, fieldName string) *ValidationError {
	err := v.validate.Var(field, tag)
	if err == nil {
		return nil
	}

	if validatorErrors, ok := err.(validator.ValidationErrors); ok && len(validatorErrors) > 0 {
		fieldError := validatorErrors[0]
		return &ValidationError{
			Field:   fieldName,
			Tag:     fieldError.Tag(),
			Value:   fmt.Sprintf("%v", fieldError.Value()),
			Message: v.formatErrorMessageWithFieldName(fieldError, fieldName),
		}
	}
	return &ValidationError{
		Field:   fieldName,
		Tag:     tag,
		Value:   fmt.Sprintf("%v", field),
		Message: err.Error(),
	}
}

// RegisterCustomValidator registers a new custom validation function
func (v *validatorService) RegisterCustomValidator(tag string, fn validator.Func) error {
	return v.validate.RegisterValidation(tag, fn)
}

// IsValidUsername validates if a string is a valid username using the internal logic
func (v *validatorService) IsValidUsername(username string) bool {
	return isValidUsernameInternal(username)
}

// IsValidEmail validates if a string is a valid email address using the internal logic
func (v *validatorService) IsValidEmail(email string) bool {
	return isValidEmailInternal(email)
}

// IsEmailOrUsername determines if an identifier is an email or username
// Returns "email", "username", or "invalid"
func (v *validatorService) IsEmailOrUsername(identifier string) string {
	identifier = strings.TrimSpace(identifier)

	if identifier == "" {
		return "invalid"
	}

	// Check if it's a valid email first
	if v.IsValidEmail(identifier) {
		return "email"
	}

	// Check if it's a valid username
	if v.IsValidUsername(identifier) {
		return "username"
	}

	return "invalid"
}

// SanitizeIdentifier cleans and normalizes an identifier
// Trims whitespace and converts to lowercase for consistent lookup
func (v *validatorService) SanitizeIdentifier(identifier string) string {
	return strings.ToLower(strings.TrimSpace(identifier))
}

// formatErrorMessage formats a validation error into a human-readable message
func (v *validatorService) formatErrorMessage(fieldError validator.FieldError) string {
	field := fieldError.Field()

	// If field is empty, use a default field name
	if field == "" {
		field = "field"
	}

	return v.formatErrorMessageWithFieldName(fieldError, field)
}

// formatErrorMessageWithFieldName formats a validation error with a specific field name
func (v *validatorService) formatErrorMessageWithFieldName(fieldError validator.FieldError, fieldName string) string {
	tag := fieldError.Tag()
	value := fieldError.Value()

	switch tag {
	case "required":
		return fmt.Sprintf("%s is required", fieldName)
	case "email":
		return fmt.Sprintf("%s must be a valid email address", fieldName)
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fieldName, fieldError.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", fieldName, fieldError.Param())
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", fieldName, fieldError.Param())
	case "username":
		return fmt.Sprintf("%s must be a valid username (3-255 characters, alphanumeric with ._- allowed, no consecutive special characters)", fieldName)
	case "email_or_username":
		return fmt.Sprintf("%s must be a valid email address or username", fieldName)
	case "eth_addr":
		return fmt.Sprintf("%s must be a valid Ethereum address", fieldName)
	case "env":
		return fmt.Sprintf("%s must be one of: development, staging, production", fieldName)
	default:
		return fmt.Sprintf("%s failed validation for tag '%s' with value '%v'", fieldName, tag, value)
	}
}

// validateUsername validates if a string is a valid username
// Rules:
// - Length between 3-255 characters
// - Must start and end with alphanumeric characters
// - Can contain alphanumeric, underscore, hyphen, and dot
// - Cannot have consecutive special characters
func validateUsername(fl validator.FieldLevel) bool {
	username := fl.Field().String()

	if len(username) < MinUsernameLength || len(username) > MaxUsernameLength {
		return false
	}

	// Check if username matches the allowed pattern
	if !usernameRegex.MatchString(username) {
		return false
	}

	// Additional check: no consecutive special characters
	return !hasConsecutiveSpecialChars(username)
}

// validateEmailOrUsername validates if a string is either a valid email or username
func validateEmailOrUsername(fl validator.FieldLevel) bool {
	identifier := strings.TrimSpace(fl.Field().String())

	if identifier == "" {
		return false
	}

	// Check if it's a valid email first
	if isValidEmailInternal(identifier) {
		return true
	}

	// Check if it's a valid username
	return isValidUsernameInternal(identifier)
}

// isValidEmailInternal validates if a string is a valid email address
func isValidEmailInternal(email string) bool {
	if len(email) == 0 || len(email) > 254 { // RFC 5321 limit
		return false
	}

	return emailRegex.MatchString(email)
}

// isValidUsernameInternal validates if a string is a valid username
func isValidUsernameInternal(username string) bool {
	if len(username) < MinUsernameLength || len(username) > MaxUsernameLength {
		return false
	}

	// Check if username matches the allowed pattern
	if !usernameRegex.MatchString(username) {
		return false
	}

	// Additional check: no consecutive special characters
	return !hasConsecutiveSpecialChars(username)
}

// hasConsecutiveSpecialChars checks if string has consecutive special characters
func hasConsecutiveSpecialChars(s string) bool {
	specialChars := "._-"
	for i := 0; i < len(s)-1; i++ {
		if strings.ContainsRune(specialChars, rune(s[i])) &&
			strings.ContainsRune(specialChars, rune(s[i+1])) {
			return true
		}
	}
	return false
}

// validateEthereumAddress validates Ethereum address format (0x + 40 hex characters)
func validateEthereumAddress(fl validator.FieldLevel) bool {
	addr := fl.Field().String()
	return len(addr) == 42 && strings.HasPrefix(addr, "0x")
}

// validateEnvironment validates environment values (development, staging, production)
func validateEnvironment(fl validator.FieldLevel) bool {
	env := fl.Field().String()
	return env == "development" || env == "staging" || env == "production"
}

// registerCommonValidators registers commonly used custom validators
func (v *validatorService) registerCommonValidators() {
	// Username validator
	if err := v.validate.RegisterValidation("username", validateUsername); err != nil {
		v.logger.Error("failed to register username validator", zap.Error(err))
	}

	// Email or username validator
	if err := v.validate.RegisterValidation("email_or_username", validateEmailOrUsername); err != nil {
		v.logger.Error("failed to register email_or_username validator", zap.Error(err))
	}

	// Ethereum address validator
	if err := v.validate.RegisterValidation("eth_addr", validateEthereumAddress); err != nil {
		v.logger.Error("failed to register eth_addr validator", zap.Error(err))
	}

	// Environment validator (already handled by oneof, but keeping for consistency)
	if err := v.validate.RegisterValidation("env", validateEnvironment); err != nil {
		v.logger.Error("failed to register env validator", zap.Error(err))
	}
}
