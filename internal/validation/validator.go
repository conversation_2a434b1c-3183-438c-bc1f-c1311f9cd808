// Package validation provides a centralized validation service for the application.
// It wraps go-playground/validator with dependency injection support and pre-registered
// common validators for consistent validation across all services.
package validation

import (
	"strings"

	validator "github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

// Validator provides validation operations for structs and fields
type Validator interface {
	// Struct validates a struct using validation tags
	Struct(s any) error
	// Field validates a single field with a validation tag
	Field(field any, tag string) error
	// RegisterCustomValidator registers a new custom validation function
	RegisterCustomValidator(tag string, fn validator.Func) error
}

// validatorService implements the Validator interface
type validatorService struct {
	validate *validator.Validate
	logger   *zap.Logger
}

// NewValidator creates a new validator service with pre-registered common validators
func NewValidator(logger *zap.Logger) Validator {
	v := validator.New(validator.WithRequiredStructEnabled())

	service := &validatorService{
		validate: v,
		logger:   logger,
	}
	service.registerCommonValidators()

	return service
}

// Struct validates a struct using validation tags
func (v *validatorService) Struct(s any) error {
	return v.validate.Struct(s)
}

// Field validates a single field with a validation tag
func (v *validatorService) Field(field any, tag string) error {
	return v.validate.Var(field, tag)
}

// RegisterCustomValidator registers a new custom validation function
func (v *validatorService) RegisterCustomValidator(tag string, fn validator.Func) error {
	return v.validate.RegisterValidation(tag, fn)
}

// validateEthereumAddress validates Ethereum address format (0x + 40 hex characters)
func validateEthereumAddress(fl validator.FieldLevel) bool {
	addr := fl.Field().String()
	return len(addr) == 42 && strings.HasPrefix(addr, "0x")
}

// validateEnvironment validates environment values (development, staging, production)
func validateEnvironment(fl validator.FieldLevel) bool {
	env := fl.Field().String()
	return env == "development" || env == "staging" || env == "production"
}

// registerCommonValidators registers commonly used custom validators
func (v *validatorService) registerCommonValidators() {
	// Ethereum address validator
	if err := v.validate.RegisterValidation("eth_addr", validateEthereumAddress); err != nil {
		v.logger.Error("failed to register eth_addr validator", zap.Error(err))
	}

	// Environment validator (already handled by oneof, but keeping for consistency)
	if err := v.validate.RegisterValidation("env", validateEnvironment); err != nil {
		v.logger.Error("failed to register env validator", zap.Error(err))
	}
}
