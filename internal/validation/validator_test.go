package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestNewValidator(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)
	assert.NotNil(t, validator)
}

func TestValidatorService_IsValidUsername(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	tests := []struct {
		name     string
		username string
		expected bool
	}{
		// Valid usernames
		{"valid simple username", "user123", true},
		{"valid with underscore", "user_123", true},
		{"valid with hyphen", "user-123", true},
		{"valid with dot", "user.123", true},
		{"valid mixed", "user_123.test-name", true},
		{"minimum length", "abc", true},
		{"alphanumeric only", "user123test", true},

		// Invalid usernames
		{"too short", "ab", false},
		{"empty string", "", false},
		{"starts with underscore", "_user", false},
		{"starts with hyphen", "-user", false},
		{"starts with dot", ".user", false},
		{"ends with underscore", "user_", false},
		{"ends with hyphen", "user-", false},
		{"ends with dot", "user.", false},
		{"consecutive underscores", "user__test", false},
		{"consecutive hyphens", "user--test", false},
		{"consecutive dots", "user..test", false},
		{"consecutive mixed", "user._test", false},
		{"contains space", "user test", false},
		{"contains special chars", "user@test", false},
		{"too long", string(make([]byte, 256)), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.IsValidUsername(tt.username)
			assert.Equal(t, tt.expected, result, "Username: %s", tt.username)
		})
	}
}

func TestValidatorService_IsValidEmail(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		// Valid emails
		{"simple email", "<EMAIL>", true},
		{"email with subdomain", "<EMAIL>", true},
		{"email with numbers", "<EMAIL>", true},
		{"email with dots", "<EMAIL>", true},
		{"email with plus", "<EMAIL>", true},
		{"email with hyphen", "<EMAIL>", true},

		// Invalid emails
		{"empty string", "", false},
		{"no @ symbol", "userexample.com", false},
		{"no domain", "user@", false},
		{"no local part", "@example.com", false},
		{"multiple @ symbols", "user@@example.com", false},
		{"no TLD", "user@example", false},
		{"invalid TLD", "user@example.c", false},
		{"too long", string(make([]byte, 255)) + "@example.com", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.IsValidEmail(tt.email)
			assert.Equal(t, tt.expected, result, "Email: %s", tt.email)
		})
	}
}

func TestValidatorService_IsEmailOrUsername(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	tests := []struct {
		name       string
		identifier string
		expected   string
	}{
		// Valid emails
		{"valid email", "<EMAIL>", "email"},
		{"email with subdomain", "<EMAIL>", "email"},

		// Valid usernames
		{"valid username", "user123", "username"},
		{"username with underscore", "user_123", "username"},
		{"username with hyphen", "user-123", "username"},

		// Invalid identifiers
		{"empty string", "", "invalid"},
		{"whitespace only", "   ", "invalid"},
		{"invalid format", "user@", "invalid"},
		{"too short username", "ab", "invalid"},
		{"invalid username chars", "user@test", "invalid"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.IsEmailOrUsername(tt.identifier)
			assert.Equal(t, tt.expected, result, "Identifier: %s", tt.identifier)
		})
	}
}

func TestValidatorService_SanitizeIdentifier(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	tests := []struct {
		name       string
		identifier string
		expected   string
	}{
		{"trim whitespace", "  <EMAIL>  ", "<EMAIL>"},
		{"convert to lowercase", "<EMAIL>", "<EMAIL>"},
		{"mixed case username", "UserName123", "username123"},
		{"already clean", "<EMAIL>", "<EMAIL>"},
		{"empty string", "", ""},
		{"whitespace only", "   ", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.SanitizeIdentifier(tt.identifier)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidatorService_Field(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	// Test username validation
	err := validator.Field("user123", "username")
	assert.NoError(t, err)

	err = validator.Field("ab", "username")
	assert.Error(t, err)

	// Test email_or_username validation
	err = validator.Field("<EMAIL>", "email_or_username")
	assert.NoError(t, err)

	err = validator.Field("user123", "email_or_username")
	assert.NoError(t, err)

	err = validator.Field("ab", "email_or_username")
	assert.Error(t, err)
}

type TestStruct struct {
	Username   string `validate:"username"`
	Identifier string `validate:"email_or_username"`
	Email      string `validate:"email"`
}

func TestValidatorService_Struct(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	// Valid struct
	validStruct := TestStruct{
		Username:   "user123",
		Identifier: "<EMAIL>",
		Email:      "<EMAIL>",
	}
	err := validator.Struct(validStruct)
	assert.NoError(t, err)

	// Invalid username
	invalidStruct := TestStruct{
		Username:   "ab",
		Identifier: "<EMAIL>",
		Email:      "<EMAIL>",
	}
	err = validator.Struct(invalidStruct)
	assert.Error(t, err)

	// Invalid identifier
	invalidStruct2 := TestStruct{
		Username:   "user123",
		Identifier: "ab",
		Email:      "<EMAIL>",
	}
	err = validator.Struct(invalidStruct2)
	assert.Error(t, err)
}

func TestHasConsecutiveSpecialChars(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{"no consecutive", "user_test", false},
		{"consecutive underscores", "user__test", true},
		{"consecutive dots", "user..test", true},
		{"consecutive hyphens", "user--test", true},
		{"consecutive mixed", "user._test", true},
		{"single special char", "user_", false},
		{"no special chars", "usertest", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := hasConsecutiveSpecialChars(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidatorServiceRegisterCustomValidator(t *testing.T) {
	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	// Test that the validator service exists and can register validators
	// (We can't easily test custom validators without importing the validator package)
	assert.NotNil(t, validator)
}

func TestValidateUsername_EdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		username string
		expected bool
	}{
		// Edge cases for length
		{"exactly min length", "abc", true},
		{"exactly max length", string(make([]byte, 255)), false}, // Will fail pattern check
		{"one char too short", "ab", false},

		// Edge cases for patterns
		{"single char", "a", false},
		{"two chars", "ab", false},
		{"starts and ends alphanumeric", "a1", false}, // too short
		{"starts and ends alphanumeric valid", "a1b", true},

		// Unicode characters
		{"unicode chars", "user测试", false},
		{"emoji", "user😀", false},

		// Special character combinations
		{"all allowed special chars", "a._-b", true},
		{"mixed special at start", ".user", false},
		{"mixed special at end", "user.", false},
	}

	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.IsValidUsername(tt.username)
			assert.Equal(t, tt.expected, result, "Username: %s", tt.username)
		})
	}
}

func TestValidateEmailOrUsername_EdgeCases(t *testing.T) {
	tests := []struct {
		name       string
		identifier string
		expected   string
	}{
		// Whitespace handling
		{"leading whitespace email", "  <EMAIL>", "email"},
		{"trailing whitespace email", "<EMAIL>  ", "email"},
		{"leading whitespace username", "  user123", "username"},
		{"trailing whitespace username", "user123  ", "username"},
		{"only whitespace", "   ", "invalid"},

		// Mixed case
		{"uppercase email", "<EMAIL>", "email"},
		{"uppercase username", "USER123", "username"},

		// Boundary cases
		{"email at max length", "a@" + string(make([]byte, 250)) + ".com", "invalid"}, // too long
		{"username at max length", string(make([]byte, 255)), "invalid"},              // will fail pattern
	}

	logger, _ := zap.NewProduction()
	validator := NewValidator(logger)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.IsEmailOrUsername(tt.identifier)
			assert.Equal(t, tt.expected, result, "Identifier: %s", tt.identifier)
		})
	}
}
