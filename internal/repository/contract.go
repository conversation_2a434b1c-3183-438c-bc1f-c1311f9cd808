package repository

import (
	"github.com/jackc/pgx/v5/pgxpool"
)

// Repository aggregates all repository interfaces
// lets accept this as a tech debt for now
type Repository struct {
	User       UserRepository
	Auth       AuthRepository
	ResetToken ResetTokenRepository
}

// NewRepository creates a new repository instance with all sub-repositories
func NewRepository(pool *pgxpool.Pool) *Repository {
	return &Repository{
		User:       NewUserRepository(pool),
		Auth:       NewAuthRepository(pool),
		ResetToken: NewResetTokenRepository(pool),
	}
}
