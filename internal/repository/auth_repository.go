package repository

import (
	"context"
	"errors"

	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5/pgxpool"

	"telescope-be/internal/entity"
)

type AuthRepository interface {
	SaveRefreshToken(ctx context.Context, token entity.RefreshToken) error
	GetRefreshToken(ctx context.Context, tokenHash string) (*entity.RefreshToken, error)
	RevokeRefreshToken(ctx context.Context, tokenHash string) error
	RevokeAllUserTokens(ctx context.Context, userID string) error
}

type authRepository struct {
	pool *pgxpool.Pool
}

func NewAuthRepository(pool *pgxpool.Pool) AuthRepository {
	return &authRepository{
		pool: pool,
	}
}

func (r *authRepository) SaveRefreshToken(ctx context.Context, token entity.RefreshToken) error {
	query := `
		INSERT INTO refresh_tokens (id, user_id, token_hash, expires_at, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err := r.pool.Exec(ctx, query,
		token.ID,
		token.UserID,
		token.TokenHash,
		token.ExpiresAt,
		token.CreatedAt,
		token.UpdatedAt,
	)

	return err
}

func (r *authRepository) RevokeRefreshToken(ctx context.Context, tokenHash string) error {
	query := `
		UPDATE refresh_tokens
		SET is_revoked = true
		WHERE token_hash = $1
	`

	_, err := r.pool.Exec(ctx, query, tokenHash)
	return err
}

func (r *authRepository) GetRefreshToken(ctx context.Context, tokenHash string) (*entity.RefreshToken, error) {
	query := `
		SELECT id, user_id, token_hash, expires_at, is_revoked, created_at, updated_at
		FROM refresh_tokens
		WHERE token_hash = $1
	`

	var token entity.RefreshToken
	err := pgxscan.Get(ctx, r.pool, &token, query, tokenHash)
	if err != nil {
		if pgxscan.NotFound(err) {
			return nil, errors.New("refresh token not found")
		}
		return nil, err
	}

	return &token, nil
}

func (r *authRepository) RevokeAllUserTokens(ctx context.Context, userID string) error {
	query := `
		UPDATE refresh_tokens
		SET is_revoked = true
		WHERE user_id = $1
	`

	_, err := r.pool.Exec(ctx, query, userID)
	return err
}
