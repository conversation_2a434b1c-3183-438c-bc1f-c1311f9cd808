// Package mappers provides data mapping utilities for repository operations.
package mappers

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"telescope-be/internal/entity"
)

// ExchangeRateFlatRow represents a flattened row from joined query for pgx struct scanning
// This handles the limitation where pgx can't directly scan nested structs from joins
type ExchangeRateFlatRow struct {
	// Exchange Rate fields
	ID             int             `db:"id"`
	ExternalID     uuid.UUID       `db:"external_id"`
	CurrencyPairID int             `db:"currency_pair_id"`
	Rate           decimal.Decimal `db:"rate"`
	Source         string          `db:"source"`
	SourceMetadata []byte          `db:"source_metadata"` // JSONB as bytes for pgx scanning
	ValidFrom      time.Time       `db:"valid_from"`
	ValidUntil     *time.Time      `db:"valid_until"`
	IsActive       bool            `db:"is_active"`
	CreatedAt      time.Time       `db:"created_at"`
	CreatedBy      *int            `db:"created_by"`
	Version        int             `db:"version"`

	// Currency Pair fields
	CurrencyPairExternalID          uuid.UUID        `db:"cp_external_id"`
	CurrencyPairFromCurrencyID      int              `db:"cp_from_currency_id"`
	CurrencyPairToCurrencyID        int              `db:"cp_to_currency_id"`
	CurrencyPairIsActive            bool             `db:"cp_is_active"`
	CurrencyPairMinAmount           decimal.Decimal  `db:"cp_min_amount"`
	CurrencyPairMaxAmount           *decimal.Decimal `db:"cp_max_amount"`
	CurrencyPairDeliveryTimeMin     int              `db:"cp_delivery_time_min"`
	CurrencyPairDeliveryTimeMax     int              `db:"cp_delivery_time_max"`
	CurrencyPairDeliveryDescription *string          `db:"cp_delivery_description"`

	// From Currency fields (flattened)
	FromCurrencyID            int       `db:"fc_id"`
	FromCurrencyExternalID    uuid.UUID `db:"fc_external_id"`
	FromCurrencyCode          string    `db:"fc_code"`
	FromCurrencyName          string    `db:"fc_name"`
	FromCurrencySymbol        string    `db:"fc_symbol"`
	FromCurrencyDecimalPlaces int       `db:"fc_decimal_places"`

	// To Currency fields (flattened)
	ToCurrencyID            int       `db:"tc_id"`
	ToCurrencyExternalID    uuid.UUID `db:"tc_external_id"`
	ToCurrencyCode          string    `db:"tc_code"`
	ToCurrencyName          string    `db:"tc_name"`
	ToCurrencySymbol        string    `db:"tc_symbol"`
	ToCurrencyDecimalPlaces int       `db:"tc_decimal_places"`
}

// ToEntity converts the flat row struct to the nested entity structure
func (flat *ExchangeRateFlatRow) ToEntity() *entity.ExchangeRate {
	// Build from currency
	fromCurrency := &entity.Currency{
		ID:            flat.FromCurrencyID,
		ExternalID:    flat.FromCurrencyExternalID,
		Code:          flat.FromCurrencyCode,
		Name:          flat.FromCurrencyName,
		Symbol:        flat.FromCurrencySymbol,
		DecimalPlaces: flat.FromCurrencyDecimalPlaces,
	}

	// Build to currency
	toCurrency := &entity.Currency{
		ID:            flat.ToCurrencyID,
		ExternalID:    flat.ToCurrencyExternalID,
		Code:          flat.ToCurrencyCode,
		Name:          flat.ToCurrencyName,
		Symbol:        flat.ToCurrencySymbol,
		DecimalPlaces: flat.ToCurrencyDecimalPlaces,
	}

	// Build currency pair with relationships
	pair := &entity.CurrencyPair{
		ID:                  flat.CurrencyPairID,
		ExternalID:          flat.CurrencyPairExternalID,
		FromCurrencyID:      flat.CurrencyPairFromCurrencyID,
		ToCurrencyID:        flat.CurrencyPairToCurrencyID,
		IsActive:            flat.CurrencyPairIsActive,
		MinAmount:           flat.CurrencyPairMinAmount,
		MaxAmount:           flat.CurrencyPairMaxAmount,
		DeliveryTimeMin:     flat.CurrencyPairDeliveryTimeMin,
		DeliveryTimeMax:     flat.CurrencyPairDeliveryTimeMax,
		DeliveryDescription: flat.CurrencyPairDeliveryDescription,
		FromCurrency:        fromCurrency,
		ToCurrency:          toCurrency,
	}

	// Parse JSONB metadata
	var sourceMetadata map[string]interface{}
	if flat.SourceMetadata != nil {
		if err := json.Unmarshal(flat.SourceMetadata, &sourceMetadata); err != nil {
			// If JSON parsing fails, create empty map
			sourceMetadata = make(map[string]interface{})
		}
	} else {
		sourceMetadata = make(map[string]interface{})
	}

	// Build exchange rate with full relationships
	return &entity.ExchangeRate{
		ID:             flat.ID,
		ExternalID:     flat.ExternalID,
		CurrencyPairID: flat.CurrencyPairID,
		Rate:           flat.Rate,
		Source:         flat.Source,
		SourceMetadata: sourceMetadata,
		ValidFrom:      flat.ValidFrom,
		ValidUntil:     flat.ValidUntil,
		IsActive:       flat.IsActive,
		CreatedAt:      flat.CreatedAt,
		CreatedBy:      flat.CreatedBy,
		Version:        flat.Version,
		CurrencyPair:   pair,
	}
}
