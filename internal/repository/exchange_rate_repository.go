package repository

import (
	"context"

	pgx "github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"telescope-be/internal/entity"
	"telescope-be/internal/repository/mappers"
)

// Temporal Validity for Exchange Rates
//
// temporal validity is implemented using valid_from and valid_until columns:
//
// valid_from (TIMESTAMP WITH TIME ZONE NOT NULL):
//   - When this exchange rate becomes active (required)
//   - Current rates: valid_from = now
//   - Historical rates: valid_from = past_date
//   - Future rates: valid_from = future_date
//
// valid_until (TIMESTAMP WITH TIME ZONE):
//   - When this exchange rate expires (optional)
//   - NULL means "current rate" (no expiration)
//   - Non-NULL means rate expires at specified time
//
// Query patterns:
//   - Current rate: WHERE valid_from <= NOW() AND (valid_until IS NULL OR valid_until > NOW())
//   - Rate at specific date: WHERE valid_from <= 'date' AND (valid_until IS NULL OR valid_until > 'date')
//   - Rate history: ORDER BY valid_from DESC

type ExchangeRateRepository interface {
	GetCurrentRate(ctx context.Context, fromCurrencyCode, toCurrencyCode string) (*entity.ExchangeRate, error)
	GetCurrentRates(ctx context.Context, activeOnly bool) ([]*entity.ExchangeRate, error)
	GetRateHistory(ctx context.Context, fromCurrencyCode, toCurrencyCode string, limit int) ([]*entity.ExchangeRate, error)
}

type exchangeRateRepository struct {
	db *pgxpool.Pool
}

func NewExchangeRateRepository(db *pgxpool.Pool) ExchangeRateRepository {
	return &exchangeRateRepository{
		db: db,
	}
}

func (r *exchangeRateRepository) GetCurrentRate(
	ctx context.Context,
	fromCurrencyCode, toCurrencyCode string,
) (*entity.ExchangeRate, error) {
	query := `
		SELECT er.id, er.external_id, er.currency_pair_id, er.rate, er.source, er.source_metadata,
		       er.valid_from, er.valid_until, er.is_active, er.created_at, er.created_by, er.version,
		       cp.id as cp_id, cp.external_id as cp_external_id, cp.from_currency_id as cp_from_currency_id,
		       cp.to_currency_id as cp_to_currency_id, cp.is_active as cp_is_active,
		       cp.min_amount as cp_min_amount, cp.max_amount as cp_max_amount,
		       cp.delivery_time_min as cp_delivery_time_min, cp.delivery_time_max as cp_delivery_time_max,
		       cp.delivery_description as cp_delivery_description,
		       fc.id as fc_id, fc.external_id as fc_external_id, fc.code as fc_code,
		       fc.name as fc_name, fc.symbol as fc_symbol, fc.decimal_places as fc_decimal_places,
		       tc.id as tc_id, tc.external_id as tc_external_id, tc.code as tc_code,
		       tc.name as tc_name, tc.symbol as tc_symbol, tc.decimal_places as tc_decimal_places
		FROM exchange_rates er
		JOIN currency_pairs cp ON er.currency_pair_id = cp.id
		JOIN currencies fc ON cp.from_currency_id = fc.id
		JOIN currencies tc ON cp.to_currency_id = tc.id
		WHERE fc.code = $1 AND tc.code = $2
		  AND er.is_active = true
		  AND er.valid_from <= CURRENT_TIMESTAMP
		  AND (er.valid_until IS NULL OR er.valid_until > CURRENT_TIMESTAMP)
		ORDER BY er.valid_from DESC
		LIMIT 1
	`

	rows, err := r.db.Query(ctx, query, fromCurrencyCode, toCurrencyCode)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	flatRow, err := pgx.CollectOneRow(rows, pgx.RowToStructByName[mappers.ExchangeRateFlatRow])
	if err != nil {
		return nil, err
	}

	return flatRow.ToEntity(), nil
}

func (r *exchangeRateRepository) GetCurrentRates(ctx context.Context, activeOnly bool) ([]*entity.ExchangeRate, error) {
	query := `
		SELECT DISTINCT ON (cp.from_currency_id, cp.to_currency_id)
		       er.id, er.external_id, er.currency_pair_id, er.rate, er.source, er.source_metadata,
		       er.valid_from, er.valid_until, er.is_active, er.created_at, er.created_by, er.version,
		       cp.id as cp_id, cp.external_id as cp_external_id, cp.from_currency_id as cp_from_currency_id,
		       cp.to_currency_id as cp_to_currency_id, cp.is_active as cp_is_active,
		       cp.min_amount as cp_min_amount, cp.max_amount as cp_max_amount,
		       cp.delivery_time_min as cp_delivery_time_min, cp.delivery_time_max as cp_delivery_time_max,
		       cp.delivery_description as cp_delivery_description,
		       fc.id as fc_id, fc.external_id as fc_external_id, fc.code as fc_code,
		       fc.name as fc_name, fc.symbol as fc_symbol, fc.decimal_places as fc_decimal_places,
		       tc.id as tc_id, tc.external_id as tc_external_id, tc.code as tc_code,
		       tc.name as tc_name, tc.symbol as tc_symbol, tc.decimal_places as tc_decimal_places
		FROM exchange_rates er
		JOIN currency_pairs cp ON er.currency_pair_id = cp.id
		JOIN currencies fc ON cp.from_currency_id = fc.id
		JOIN currencies tc ON cp.to_currency_id = tc.id
		WHERE er.valid_from <= CURRENT_TIMESTAMP
		  AND (er.valid_until IS NULL OR er.valid_until > CURRENT_TIMESTAMP)
	`

	args := []any{}
	if activeOnly {
		query += " AND er.is_active = $1 AND cp.is_active = $2"
		args = append(args, true, true)
	}

	query += " ORDER BY cp.from_currency_id, cp.to_currency_id, er.valid_from DESC"

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	flatRows, err := pgx.CollectRows(rows, pgx.RowToStructByName[mappers.ExchangeRateFlatRow])
	if err != nil {
		return nil, err
	}

	rates := make([]*entity.ExchangeRate, len(flatRows))
	for i, flat := range flatRows {
		rates[i] = flat.ToEntity()
	}

	return rates, nil
}

func (r *exchangeRateRepository) GetRateHistory(
	ctx context.Context,
	fromCurrencyCode, toCurrencyCode string,
	limit int,
) ([]*entity.ExchangeRate, error) {
	query := `
		SELECT er.id, er.external_id, er.currency_pair_id, er.rate, er.source, er.source_metadata,
		       er.valid_from, er.valid_until, er.is_active, er.created_at, er.created_by, er.version,
		       cp.id as cp_id, cp.external_id as cp_external_id, cp.from_currency_id as cp_from_currency_id,
		       cp.to_currency_id as cp_to_currency_id, cp.is_active as cp_is_active,
		       cp.min_amount as cp_min_amount, cp.max_amount as cp_max_amount,
		       cp.delivery_time_min as cp_delivery_time_min, cp.delivery_time_max as cp_delivery_time_max,
		       cp.delivery_description as cp_delivery_description,
		       fc.id as fc_id, fc.external_id as fc_external_id, fc.code as fc_code,
		       fc.name as fc_name, fc.symbol as fc_symbol, fc.decimal_places as fc_decimal_places,
		       tc.id as tc_id, tc.external_id as tc_external_id, tc.code as tc_code,
		       tc.name as tc_name, tc.symbol as tc_symbol, tc.decimal_places as tc_decimal_places
		FROM exchange_rates er
		JOIN currency_pairs cp ON er.currency_pair_id = cp.id
		JOIN currencies fc ON cp.from_currency_id = fc.id
		JOIN currencies tc ON cp.to_currency_id = tc.id
		WHERE fc.code = $1 AND tc.code = $2
		ORDER BY er.valid_from DESC
		LIMIT $3
	`

	rows, err := r.db.Query(ctx, query, fromCurrencyCode, toCurrencyCode, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	flatRows, err := pgx.CollectRows(rows, pgx.RowToStructByName[mappers.ExchangeRateFlatRow])
	if err != nil {
		return nil, err
	}

	rates := make([]*entity.ExchangeRate, len(flatRows))
	for i, flat := range flatRows {
		rates[i] = flat.ToEntity()
	}

	return rates, nil
}
