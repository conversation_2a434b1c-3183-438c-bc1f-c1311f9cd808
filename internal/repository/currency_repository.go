package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"

	"telescope-be/internal/entity"
)

type CurrencyRepository interface {
	Create(ctx context.Context, currency *entity.Currency) (*entity.Currency, error)
	GetByExternalID(ctx context.Context, externalID uuid.UUID) (*entity.Currency, error)
	GetByCode(ctx context.Context, code string) (*entity.Currency, error)
	List(ctx context.Context, activeOnly bool, offset, limit int) ([]*entity.Currency, error)
	Update(ctx context.Context, currency *entity.Currency) (*entity.Currency, error)
	ExistsByCode(ctx context.Context, code string) (bool, error)
}

type currencyRepository struct {
	db *pgxpool.Pool
}

func NewCurrencyRepository(db *pgxpool.Pool) CurrencyRepository {
	return &currencyRepository{
		db: db,
	}
}

func (r *currencyRepository) Create(ctx context.Context, currency *entity.Currency) (*entity.Currency, error) {
	query := `
		INSERT INTO currencies (code, name, symbol, decimal_places, country_id, is_active, created_by, updated_by)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, external_id, created_at, updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		currency.Code, currency.Name, currency.Symbol, currency.DecimalPlaces,
		currency.CountryID, currency.IsActive, currency.CreatedBy, currency.UpdatedBy,
	).Scan(&currency.ID, &currency.ExternalID, &currency.CreatedAt, &currency.UpdatedAt, &currency.Version)
	if err != nil {
		return nil, err
	}

	return currency, nil
}

func (r *currencyRepository) GetByExternalID(ctx context.Context, externalID uuid.UUID) (*entity.Currency, error) {
	query := `
		SELECT c.id, c.external_id, c.code, c.name, c.symbol, c.decimal_places, c.country_id,
		       c.is_active, c.created_at, c.updated_at, c.created_by, c.updated_by, c.version
		FROM currencies c
		WHERE c.external_id = $1 AND c.deleted_at IS NULL
	`

	var currency entity.Currency
	err := pgxscan.Get(ctx, r.db, &currency, query, externalID)
	if err != nil {
		if pgxscan.NotFound(err) {
			return nil, errors.New("currency not found")
		}
		return nil, err
	}

	return &currency, nil
}

func (r *currencyRepository) GetByCode(ctx context.Context, code string) (*entity.Currency, error) {
	query := `
		SELECT id, external_id, code, name, symbol, decimal_places, country_id,
		       is_active, created_at, updated_at, created_by, updated_by, version
		FROM currencies
		WHERE code = $1
	`

	var currency entity.Currency
	err := pgxscan.Get(ctx, r.db, &currency, query, code)
	if err != nil {
		return nil, errors.Join(err, fmt.Errorf("unknown currency code %s", code))
	}

	return &currency, nil
}

func (r *currencyRepository) List(ctx context.Context, activeOnly bool, offset, limit int) ([]*entity.Currency, error) {
	if offset < 0 {
		return nil, errors.New("offset must be >= 0")
	}
	if limit < 0 {
		return nil, errors.New("limit must be >= 0")
	}

	baseQuery := `
		SELECT id, external_id, code, name, symbol, decimal_places, country_id,
		       is_active, created_at, updated_at, created_by, updated_by, version
		FROM currencies
		WHERE deleted_at IS NULL
	`

	query, args := r.buildListQuery(baseQuery, activeOnly, offset, limit)

	var currencies []*entity.Currency
	err := pgxscan.Select(ctx, r.db, &currencies, query, args...)
	if err != nil {
		return nil, err
	}

	return currencies, nil
}

func (r *currencyRepository) Update(ctx context.Context, currency *entity.Currency) (*entity.Currency, error) {
	query := `
		UPDATE currencies
		SET name = $1, symbol = $2, decimal_places = $3, is_active = $4,
		    updated_at = CURRENT_TIMESTAMP, updated_by = $5, version = version + 1
		WHERE external_id = $6 AND version = $7
		RETURNING updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		currency.Name, currency.Symbol, currency.DecimalPlaces,
		currency.IsActive, currency.UpdatedBy, currency.ExternalID, currency.Version,
	).Scan(&currency.UpdatedAt, &currency.Version)
	if err != nil {
		return nil, err
	}

	return currency, nil
}

func (r *currencyRepository) ExistsByCode(ctx context.Context, code string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM currencies WHERE code = $1)`

	var exists bool
	err := r.db.QueryRow(ctx, query, code).Scan(&exists)
	return exists, err
}

func (r *currencyRepository) buildListQuery(baseQuery string, activeOnly bool, offset, limit int) (string, []any) {
	var args []any
	argIndex := 1

	query := baseQuery
	if activeOnly {
		query += fmt.Sprintf(" AND is_active = $%d", argIndex)
		args = append(args, true)
		argIndex++
	}

	query += " ORDER BY code"

	// Add pagination clauses
	query = r.addPaginationClause(query, limit, offset, argIndex, &args)

	return query, args
}

// addPaginationClause adds LIMIT and OFFSET clauses to the query
func (r *currencyRepository) addPaginationClause(query string, limit, offset, startIndex int, args *[]any) string {
	if limit <= 0 && offset <= 0 {
		return query
	}

	if limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", startIndex)
		*args = append(*args, limit)
		startIndex++
	}

	if offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", startIndex)
		*args = append(*args, offset)
	}

	return query
}
