package repository

import (
	"context"

	"github.com/google/uuid"
	pgx "github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"telescope-be/internal/entity"
)

type CountryRepository interface {
	Create(ctx context.Context, country *entity.Country) (*entity.Country, error)
	GetByExternalID(ctx context.Context, externalID uuid.UUID) (*entity.Country, error)
	GetByCode(ctx context.Context, code string) (*entity.Country, error)
	List(ctx context.Context, activeOnly bool) ([]*entity.Country, error)
	Update(ctx context.Context, country *entity.Country) (*entity.Country, error)
	UpdateStatus(ctx context.Context, externalID uuid.UUID, isActive bool) error
	ExistsByCode(ctx context.Context, code string) (bool, error)
}

type countryRepository struct {
	db *pgxpool.Pool
}

func NewCountryRepository(db *pgxpool.Pool) CountryRepository {
	return &countryRepository{
		db: db,
	}
}

func (r *countryRepository) Create(ctx context.Context, country *entity.Country) (*entity.Country, error) {
	query := `
		INSERT INTO countries (code, name, flag_emoji, is_active, supports_sending, supports_receiving, created_by, updated_by)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, external_id, created_at, updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		country.Code, country.Name, country.FlagEmoji,
		country.IsActive, country.SupportsSending, country.SupportsReceiving,
		country.CreatedBy, country.UpdatedBy,
	).Scan(&country.ID, &country.ExternalID, &country.CreatedAt, &country.UpdatedAt, &country.Version)
	if err != nil {
		return nil, err
	}

	return country, nil
}

func (r *countryRepository) GetByExternalID(ctx context.Context, externalID uuid.UUID) (*entity.Country, error) {
	query := `
		SELECT id, external_id, code, name, flag_emoji, is_active, supports_sending, supports_receiving,
		       created_at, updated_at, created_by, updated_by, version
		FROM countries
		WHERE external_id = $1
	`

	var country entity.Country
	err := r.db.QueryRow(ctx, query, externalID).Scan(
		&country.ID, &country.ExternalID, &country.Code, &country.Name,
		&country.FlagEmoji, &country.IsActive, &country.SupportsSending,
		&country.SupportsReceiving, &country.CreatedAt, &country.UpdatedAt,
		&country.CreatedBy, &country.UpdatedBy, &country.Version,
	)
	if err != nil {
		return nil, err
	}

	return &country, nil
}

func (r *countryRepository) GetByCode(ctx context.Context, code string) (*entity.Country, error) {
	query := `
		SELECT id, external_id, code, name, flag_emoji, is_active, supports_sending, supports_receiving,
		       created_at, updated_at, created_by, updated_by, version
		FROM countries
		WHERE code = $1
	`

	var country entity.Country
	err := r.db.QueryRow(ctx, query, code).Scan(
		&country.ID, &country.ExternalID, &country.Code, &country.Name,
		&country.FlagEmoji, &country.IsActive, &country.SupportsSending,
		&country.SupportsReceiving, &country.CreatedAt, &country.UpdatedAt,
		&country.CreatedBy, &country.UpdatedBy, &country.Version,
	)
	if err != nil {
		return nil, err
	}

	return &country, nil
}

func (r *countryRepository) List(ctx context.Context, activeOnly bool) ([]*entity.Country, error) {
	query := `
		SELECT id, external_id, code, name, flag_emoji, is_active, supports_sending, supports_receiving,
		       created_at, updated_at, created_by, updated_by, version
		FROM countries
	`

	args := []interface{}{}
	if activeOnly {
		query += " WHERE is_active = $1"
		args = append(args, true)
	}

	query += " ORDER BY name"

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var countries []*entity.Country
	for rows.Next() {
		var country entity.Country
		err := rows.Scan(
			&country.ID, &country.ExternalID, &country.Code, &country.Name,
			&country.FlagEmoji, &country.IsActive, &country.SupportsSending,
			&country.SupportsReceiving, &country.CreatedAt, &country.UpdatedAt,
			&country.CreatedBy, &country.UpdatedBy, &country.Version,
		)
		if err != nil {
			return nil, err
		}
		countries = append(countries, &country)
	}

	return countries, rows.Err()
}

func (r *countryRepository) Update(ctx context.Context, country *entity.Country) (*entity.Country, error) {
	query := `
		UPDATE countries
		SET name = $1, flag_emoji = $2, supports_sending = $3, supports_receiving = $4,
		    updated_at = CURRENT_TIMESTAMP, updated_by = $5, version = version + 1
		WHERE external_id = $6 AND version = $7
		RETURNING updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		country.Name, country.FlagEmoji, country.SupportsSending,
		country.SupportsReceiving, country.UpdatedBy, country.ExternalID, country.Version,
	).Scan(&country.UpdatedAt, &country.Version)
	if err != nil {
		return nil, err
	}

	return country, nil
}

func (r *countryRepository) UpdateStatus(ctx context.Context, externalID uuid.UUID, isActive bool) error {
	query := `
		UPDATE countries
		SET is_active = $1, updated_at = CURRENT_TIMESTAMP
		WHERE external_id = $2
	`

	result, err := r.db.Exec(ctx, query, isActive, externalID)
	if err != nil {
		return err
	}

	if result.RowsAffected() == 0 {
		return pgx.ErrNoRows
	}

	return nil
}

func (r *countryRepository) ExistsByCode(ctx context.Context, code string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM countries WHERE code = $1)`

	var exists bool
	err := r.db.QueryRow(ctx, query, code).Scan(&exists)
	return exists, err
}
