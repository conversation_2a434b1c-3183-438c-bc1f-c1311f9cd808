package repository

import (
	"context"
	"errors"
	"telescope-be/internal/entity"

	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// ResetTokenRepository defines the interface for reset token-related database operations
type ResetTokenRepository interface {
	CreateResetToken(ctx context.Context, resetToken entity.ResetToken) error
	GetResetTokenByID(ctx context.Context, id string) (*entity.ResetToken, error)
	UseResetToken(ctx context.Context, id string) error
}

// resetTokenRepository implements the ResetTokenRepository interface
type resetTokenRepository struct {
	db *pgxpool.Pool
}

// NewResetTokenRepository creates a new ResetTokenRepository instance
func NewResetTokenRepository(db *pgxpool.Pool) ResetTokenRepository {
	return &resetTokenRepository{
		db: db,
	}
}

// CreateResetToken inserts a new reset token into the database and returns the created token.
//
// Parameters:
//   - ctx: The context for managing request-scoped values, deadlines, and cancellation signals.
//   - resetToken: The entity.ResetToken object containing the details of the token to be created.
//
// Returns:
//   - A pointer to the created entity.ResetToken object if successful.
//   - An error if the operation fails.
func (r *resetTokenRepository) CreateResetToken(ctx context.Context, resetToken entity.ResetToken) error {
	var (
		eventName = "resettoken.repository.CreateResetToken"
		lf        = []zapcore.Field{
			zap.String("event_name", eventName),
			zap.Any("reset_token", resetToken),
		}
		sa = []attribute.KeyValue{
			attribute.String("reset_token_external_id", resetToken.ExternalID.String()),
			attribute.Int("user_id", resetToken.UserID),
			attribute.String("token", resetToken.Token),
		}
	)
	ctx, span := otel.Tracer("resettoken.repository").Start(ctx, eventName)
	defer span.End()
	span.SetAttributes(sa...)

	query := `
		INSERT INTO reset_tokens (external_id, user_id, token, created_at, expires_at)
		VALUES ($1, $2, $3, $4, $5)
	`
	_, err := r.db.Exec(
		ctx,
		query,
		resetToken.ExternalID,
		resetToken.UserID,
		resetToken.Token,
		resetToken.CreatedAt,
		resetToken.ExpiresAt,
	)
	if err != nil {
		span.RecordError(err)
		zap.L().Error("error creating reset token", append(lf, zap.Error(err))...)
		return err
	}

	return nil
}

// GetResetTokenByID retrieves a reset token by its ID from the database.
// It returns the token if found, or nil if not found.
//
// Parameters:
//   - ctx: The context for managing request-scoped values, deadlines, and cancellation signals.
//   - id: The ID of the reset token to retrieve.
//
// Returns:
//   - A pointer to the entity.ResetToken object if found.
//   - An error if the operation fails or if the token is not found.
func (r *resetTokenRepository) GetResetTokenByID(ctx context.Context, id string) (*entity.ResetToken, error) {
	var (
		eventName = "resettoken.repository.GetResetTokenByID"
		lf        = []zapcore.Field{
			zap.String("event_name", eventName),
			zap.String("reset_token_id", id),
		}
		sa = []attribute.KeyValue{
			attribute.String("reset_token_id", id),
		}
		resetToken entity.ResetToken
	)
	ctx, span := otel.Tracer("resettoken.repository").Start(ctx, eventName)
	defer span.End()
	span.SetAttributes(sa...)

	query := `
		SELECT id, user_id, token, created_at, expires_at, used_at
		FROM reset_tokens
		WHERE external_id = $1
	`
	err := pgxscan.Get(ctx, r.db, &resetToken, query, id)
	if err != nil {
		if pgxscan.NotFound(err) {
			return nil, errors.New("token not found") // Token not found
		}
		span.RecordError(err)
		zap.L().Error("error retrieving reset token by ID", append(lf, zap.Error(err))...)
		return nil, err
	}

	return &resetToken, nil
}

// UseResetToken marks a reset token as used by updating its used_at timestamp.
// It returns the updated token if successful, or nil if the token was not found.
//
// Parameters:
//   - ctx: The context for managing request-scoped values, deadlines, and cancellation signals.
//   - id: The ID of the reset token to mark as used.
//
// Returns:
//   - A pointer to the updated entity.ResetToken object if successful.
//   - An error if the operation fails or if the token is not found.
func (r *resetTokenRepository) UseResetToken(ctx context.Context, id string) error {
	var (
		eventName = "resettoken.repository.UseResetToken"
		lf        = []zapcore.Field{
			zap.String("event_name", eventName),
			zap.String("reset_token_id", id),
		}
		sa = []attribute.KeyValue{
			attribute.String("reset_token_id", id),
		}
	)
	ctx, span := otel.Tracer("resettoken.repository").Start(ctx, eventName)
	defer span.End()
	span.SetAttributes(sa...)

	query := `
		UPDATE reset_tokens
		SET used_at = NOW()
		WHERE external_id = $1
	`
	_, err := r.db.Exec(ctx, query, id)
	if err != nil {
		span.RecordError(err)
		zap.L().Error("error using reset token", append(lf, zap.Error(err))...)
		return err
	}

	return nil
}
