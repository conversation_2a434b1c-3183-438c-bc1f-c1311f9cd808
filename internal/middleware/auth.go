// Package middleware provides HTTP middleware functions for request processing.
package middleware

import (
	"net/http"
	"strings"

	echo "github.com/labstack/echo/v4"

	"telescope-be/internal/appctx"
	"telescope-be/internal/helper"
)

func JWTAuth(jwtHelper *helper.JWTHelper) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return c.JSON(http.StatusUnauthorized, appctx.NewResponseBuilder().
					WithMessage("Authorization header required").
					WithError("MISSING_AUTH_HEADER"))
			}

			// Check Bearer format
			tokenParts := strings.Split(authHeader, " ")
			if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
				return c.JSON(http.StatusUnauthorized, appctx.NewResponseBuilder().
					WithMessage("Invalid authorization format").
					WithError("INVALID_AUTH_FORMAT"))
			}

			// Validate token
			claims, err := jwtHelper.ValidateToken(tokenParts[1])
			if err != nil {
				return c.JSON(http.StatusUnauthorized, appctx.NewResponseBuilder().
					WithMessage("Invalid or expired token").
					WithError("INVALID_TOKEN"))
			}

			// Set user context
			c.Set("user_id", claims.UserID)
			c.Set("user_email", claims.Email)

			return next(c)
		}
	}
}
