// Package server provides HTTP server implementation with graceful shutdown.
package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	echo "github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/labstack/gommon/log"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
	"telescope-be/internal/routes"
)

type httpServer struct {
	Config appctx.ServerConfig
	server *echo.Echo
	port   string
	routes *routes.Routes
	log    *zap.Logger
}

// NewHTTPServer creates a new HTTP server with injected routes and Echo instance
func NewHTTPServer(config appctx.ServerConfig, routes *routes.Routes, e *echo.Echo, log *zap.Logger) Server {
	return &httpServer{
		Config: config,
		server: e,
		port:   config.Port,
		routes: routes,
		log:    log,
	}
}

func (s *httpServer) Start() error {
	// Set log level
	s.server.Logger.SetLevel(log.INFO)

	// Add CORS middleware
	s.server.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: s.Config.AllowedOrigins,
		AllowMethods: []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
		AllowHeaders: []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
	}))

	// Setup routes using the injected routes instance
	s.routes.SetupRoutes(s.server)

	// Log available endpoints if DEBUG level
	routes := s.server.Routes()
	s.log.Debug("Available Endpoints:")
	for _, route := range routes {
		s.log.Debug("Route", zap.String("method", route.Method), zap.String("path", route.Path))
	}

	// Start server in a goroutine
	go func() {
		if err := s.server.Start(fmt.Sprintf(":%s", s.port)); err != nil && !errors.Is(err, http.ErrServerClosed) {
			s.server.Logger.Fatalf("failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// Gracefully shutdown the server
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := s.server.Shutdown(ctx); err != nil {
		s.server.Logger.Fatal(err)
	}

	return nil
}

func (s *httpServer) Stop() error {
	if s.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return s.server.Shutdown(ctx)
	}
	return nil
}
