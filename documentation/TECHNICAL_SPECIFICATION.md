# Telescope Backend - Technical Specification

## Table of Contents
1. [Technology Stack](#technology-stack)
2. [Code Organization](#code-organization)
3. [Implementation Details](#implementation-details)
4. [Data Flow](#data-flow)
5. [Error Handling Implementation](#error-handling-implementation)
6. [Configuration Implementation](#configuration-implementation)
7. [Database Implementation](#database-implementation)
8. [HTTP Server Implementation](#http-server-implementation)
9. [Testing Implementation](#testing-implementation)
10. [Performance Implementation](#performance-implementation)
11. [Security Implementation](#security-implementation)
12. [Deployment Implementation](#deployment-implementation)

## Technology Stack

### Core Technologies
- **Language**: Go 1.24+
- **HTTP Framework**: Echo v4.13.4
- **CLI Framework**: Cobra v1.9.1
- **Configuration**: Viper v1.20.1
- **Database Migrations**: Goose v3.18.0
- **YAML Parsing**: yaml.v3 v3.0.1

### Development Tools
- **Build Tool**: Go modules
- **Testing**: Go testing framework
- **Linting**: golangci-lint
- **Formatting**: go fmt
- **Dependency Management**: go mod

### External Dependencies
- **HTTP Server**: Echo framework for high-performance HTTP handling
- **CLI**: Cobra for command-line interface
- **Config**: Viper for configuration management
- **Migrations**: Goose for database schema management

## Code Organization

### Package Structure
```
internal/
├── appctx/           # Application context and configuration
├── constants/        # Application constants
├── entity/           # Domain entities
├── handler/          # HTTP request handlers
├── middleware/       # HTTP middleware
├── presentation/     # Request/Response DTOs
├── repository/       # Data access layer
├── routes/           # HTTP routing configuration
├── server/           # HTTP server implementation
└── service/          # Business logic layer
```

### Import Organization
```go
// Standard library imports
import (
    "context"
    "fmt"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"
)

// Third-party imports
import (
    "github.com/labstack/echo/v4"
    "github.com/labstack/echo/v4/middleware"
    "github.com/labstack/gommon/log"
)

// Internal imports
import (
    "telescope-be/internal/appctx"
    "telescope-be/internal/routes"
)
```

## Implementation Details

### Main Application Entry Point
```go
func main() {
    if err := rootCmd.Execute(); err != nil {
        fmt.Fprintf(os.Stderr, "Error: %v\n", err)
        os.Exit(1)
    }
}
```

### Command Structure
```go
var rootCmd = &cobra.Command{
    Use:   "telescope",
    Short: "Telescope Backend - A Go-based HTTP server with database migrations",
    Long:  `Telescope Backend is a Go-based HTTP server with graceful shutdown...`,
}

var serverCmd = &cobra.Command{
    Use:   "server",
    Short: "Start the HTTP server",
    RunE:  func(cmd *cobra.Command, args []string) error {
        return commands.RunServer(config)
    },
}
```

### Configuration Loading
```go
func initConfig() {
    if cfgFile != "" {
        viper.SetConfigFile(cfgFile)
    } else {
        viper.AddConfigPath(".")
        viper.AddConfigPath("configs")
        viper.SetConfigType("yaml")
        viper.SetConfigName("config")
    }

    viper.AutomaticEnv()

    if err := viper.ReadInConfig(); err == nil {
        fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
    }

    var err error
    config, err = appctx.LoadConfig()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error loading config: %v\n", err)
        os.Exit(1)
    }
}
```

## Data Flow

### Request Flow
```
HTTP Request → Echo Router → Middleware → Handler → Service → Repository → Database
                ↓
HTTP Response ← Echo Router ← Middleware ← Handler ← Service ← Repository ← Database
```

### Detailed Flow
1. **HTTP Request**: Client sends request to server
2. **Echo Router**: Routes request to appropriate handler
3. **Middleware**: Applies CORS, logging, recovery middleware
4. **Handler**: Parses request, validates input, calls service
5. **Service**: Implements business logic, calls repository
6. **Repository**: Handles data access, executes database queries
7. **Database**: Stores/retrieves data
8. **Response**: Data flows back through layers to client

### Example Flow for Duck Creation
```go
// 1. HTTP Request arrives at POST /api/v1/duck
// 2. Echo routes to DuckHandler.CreateDuck
func (h *DuckHandler) CreateDuck(c echo.Context) error {
    // 3. Parse and validate request
    var req presentation.DuckCreateRequest
    if err := c.Bind(&req); err != nil {
        return err
    }

    // 4. Call service layer
    response, err := h.service.CreateDuck(req)
    if err != nil {
        return err
    }

    // 5. Return response
    return c.JSON(http.StatusCreated, response)
}
```

## Error Handling Implementation

### Error Types
```go
// Validation errors
type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

// Business logic errors
type BusinessError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
}

// Infrastructure errors
type InfrastructureError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}
```

### Error Response Structure
```go
type ErrorResponse struct {
    Error struct {
        Code    string                 `json:"code"`
        Message string                 `json:"message"`
        Details map[string]interface{} `json:"details,omitempty"`
    } `json:"error"`
}
```

### Error Handling Middleware
```go
func ErrorHandler(err error, c echo.Context) {
    var statusCode int
    var errorResponse ErrorResponse

    switch e := err.(type) {
    case *ValidationError:
        statusCode = http.StatusBadRequest
        errorResponse.Error.Code = "VALIDATION_ERROR"
        errorResponse.Error.Message = e.Message
    case *BusinessError:
        statusCode = http.StatusUnprocessableEntity
        errorResponse.Error.Code = e.Code
        errorResponse.Error.Message = e.Message
    default:
        statusCode = http.StatusInternalServerError
        errorResponse.Error.Code = "INTERNAL_ERROR"
        errorResponse.Error.Message = "An unexpected error occurred"
    }

    c.JSON(statusCode, errorResponse)
}
```

## Configuration Implementation

### Configuration Structure
```go
type Config struct {
    Server   ServerConfig   `yaml:"server"`
    Database DatabaseConfig `yaml:"database"`
    Logger   LoggerConfig   `yaml:"logger"`
    Provider ProviderConfig `yaml:"provider"`
}

type ServerConfig struct {
    Host           string   `yaml:"host"`
    Port           string   `yaml:"port"`
    Mode           string   `yaml:"mode"`
    AllowedOrigins []string `yaml:"allowed_origins"`
}
```

### Configuration Loading
```go
func LoadConfig() (*Config, error) {
    configPath := getConfigPath()

    data, err := os.ReadFile(configPath)
    if err != nil {
        return nil, err
    }

    var config Config
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, err
    }

    return &config, nil
}

func getConfigPath() string {
    if path := os.Getenv("CONFIG_PATH"); path != "" {
        return path
    }

    paths := []string{
        "config.yaml",
        "configs/config.yaml",
        "internal/config/config.yaml",
    }

    for _, path := range paths {
        if _, err := os.Stat(path); err == nil {
            return path
        }
    }

    return "config.yaml"
}
```

### Environment Variable Override
```go
// Viper automatically reads environment variables
// CONFIG_PATH can override config file location
// SERVER_PORT can override server.port
// DATABASE_HOST can override database.host
```

## Database Implementation

### Migration Strategy
```sql
-- +goose Up
CREATE TABLE IF NOT EXISTS hello_world (
    id SERIAL PRIMARY KEY,
    message TEXT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- +goose Down
DROP TABLE IF EXISTS hello_world;
```

### Migration Commands
```bash
# Apply migrations
go run main.go migrate up

# Rollback migrations
go run main.go migrate down

# Check status
go run main.go migrate status

# Reset all migrations
go run main.go migrate reset
```

### Database Configuration
```yaml
database:
  driver: "postgres"
  host: "localhost"
  port: "5432"
  user: "postgres"
  password: "password"
  database: "telescope"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
```

## HTTP Server Implementation

### Server Structure
```go
type httpServer struct {
    Config appctx.ServerConfig
    server *echo.Echo
    port   string
}

func NewHTTPServer(config appctx.ServerConfig) Server {
    return &httpServer{
        Config: config,
        port:   config.Port,
    }
}
```

### Server Initialization
```go
func (s *httpServer) Start() error {
    // Create Echo instance
    s.server = echo.New()

    // Set log level
    s.server.Logger.SetLevel(log.INFO)

    // Add middleware
    s.server.Use(middleware.Logger())
    s.server.Use(middleware.Recover())
    s.server.Use(middleware.CORSWithConfig(middleware.CORSConfig{
        AllowOrigins: s.Config.AllowedOrigins,
        AllowMethods: []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
        AllowHeaders: []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
    }))

    // Setup routes
    routes.SetupRoutes(s.server)

    // Start server in goroutine
    go func() {
        if err := s.server.Start(fmt.Sprintf(":%s", s.port)); err != nil && err != http.ErrServerClosed {
            s.server.Logger.Fatal("shutting down the server")
        }
    }()

    // Wait for interrupt signal
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    // Graceful shutdown
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()

    return s.server.Shutdown(ctx)
}
```

### Route Setup
```go
func SetupRoutes(e *echo.Echo) {
    service := service.NewService(&service.Dependencies{})
    handler := http.NewHTTPHandler(&http.Dependencies{Service: service})
    dependencies := &Dependencies{HTTPHandler: handler}

    // API group
    api := e.Group("/api")

    // v1 group
    v1 := api.Group("/v1")

    SetupDuckRoutes(v1, dependencies)
}

func SetupDuckRoutes(e *echo.Group, dependencies *Dependencies) {
    duckGroup := e.Group("/duck")
    duckGroup.GET("", dependencies.HTTPHandler.DuckHandler.GetDuck)
    duckGroup.POST("", dependencies.HTTPHandler.DuckHandler.CreateDuck)
}
```

## Testing Implementation

### Test Structure
```go
// handler_test.go
func TestDuckHandler_CreateDuck(t *testing.T) {
    // Setup
    mockService := &MockDuckService{}
    handler := NewDuckHandler(mockService)

    // Test cases
    tests := []struct {
        name           string
        request        presentation.DuckCreateRequest
        expectedStatus int
        expectedError  bool
    }{
        {
            name: "Valid request",
            request: presentation.DuckCreateRequest{
                Name:  "Donald",
                Breed: "Mallard",
                Sound: "Quack",
            },
            expectedStatus: http.StatusCreated,
            expectedError:  false,
        },
        // Add more test cases
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

### Mock Implementation
```go
type MockDuckService struct {
    mock.Mock
}

func (m *MockDuckService) CreateDuck(request presentation.DuckCreateRequest) (*presentation.DuckCreateResponse, error) {
    args := m.Called(request)
    return args.Get(0).(*presentation.DuckCreateResponse), args.Error(1)
}

func (m *MockDuckService) GetDuck() (*presentation.DuckGetResponse, error) {
    args := m.Called()
    return args.Get(0).(*presentation.DuckGetResponse), args.Error(1)
}
```

### Integration Tests
```go
func TestDuckAPI_Integration(t *testing.T) {
    // Setup test server
    e := echo.New()
    service := service.NewService(&service.Dependencies{})
    handler := http.NewHTTPHandler(&http.Dependencies{Service: service})

    // Setup routes
    api := e.Group("/api")
    v1 := api.Group("/v1")
    duckGroup := v1.Group("/duck")
    duckGroup.POST("", handler.DuckHandler.CreateDuck)

    // Test request
    req := httptest.NewRequest(http.MethodPost, "/api/v1/duck", strings.NewReader(`{"name":"Donald","breed":"Mallard","sound":"Quack"}`))
    req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
    rec := httptest.NewRecorder()

    e.ServeHTTP(rec, req)

    // Assertions
    assert.Equal(t, http.StatusCreated, rec.Code)

    var response presentation.DuckCreateResponse
    err := json.Unmarshal(rec.Body.Bytes(), &response)
    assert.NoError(t, err)
    assert.Equal(t, "Donald", response.Name)
}
```

## Performance Implementation

### HTTP Server Optimization
```go
// Echo framework provides high performance out of the box
// Additional optimizations can be added:

// Compression middleware
s.server.Use(middleware.Gzip())

// Rate limiting middleware
s.server.Use(middleware.RateLimiter(middleware.RateLimiterConfig{
    Store: middleware.NewRateLimiterMemoryStore(
        middleware.RateLimiterMemoryStoreConfig{
            Rate:      10,
            Burst:     30,
            ExpiresIn: time.Minute,
        },
    ),
}))

// Caching middleware
s.server.Use(middleware.CacheWithConfig(middleware.CacheConfig{
    Store: middleware.NewCacheMemoryStore(time.Minute),
}))
```

### Database Optimization
```go
// Connection pooling configuration
type DatabaseConfig struct {
    MaxOpenConns    int    `yaml:"max_open_conns"`
    MaxIdleConns    int    `yaml:"max_idle_conns"`
    ConnMaxLifetime string `yaml:"conn_max_lifetime"`
}

// Query optimization
func (r *duckRepository) GetDuck() (*entity.Duck, error) {
    // Use prepared statements
    stmt, err := r.db.Prepare("SELECT name, breed, sound FROM ducks LIMIT 1")
    if err != nil {
        return nil, err
    }
    defer stmt.Close()

    // Execute with context for timeout
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    var duck entity.Duck
    err = stmt.QueryRowContext(ctx).Scan(&duck.Name, &duck.Breed, &duck.Sound)
    if err != nil {
        return nil, err
    }

    return &duck, nil
}
```

### Memory Management
```go
// Proper resource cleanup
func (s *httpServer) Stop() error {
    if s.server != nil {
        ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
        defer cancel()
        return s.server.Shutdown(ctx)
    }
    return nil
}

// Connection cleanup
func (r *duckRepository) Close() error {
    if r.db != nil {
        return r.db.Close()
    }
    return nil
}
```

## Security Implementation

### Input Validation
```go
type DuckCreateRequest struct {
    Name  string `json:"name" validate:"required,min=1,max=100"`
    Breed string `json:"breed" validate:"required,min=1,max=50"`
    Sound string `json:"sound" validate:"required,min=1,max=50"`
}

// Validation middleware
func ValidateRequest(next echo.HandlerFunc) echo.HandlerFunc {
    return func(c echo.Context) error {
        var req presentation.DuckCreateRequest
        if err := c.Bind(&req); err != nil {
            return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
        }

        if err := validate.Struct(req); err != nil {
            return echo.NewHTTPError(http.StatusBadRequest, "Validation failed")
        }

        return next(c)
    }
}
```

### CORS Configuration
```go
s.server.Use(middleware.CORSWithConfig(middleware.CORSConfig{
    AllowOrigins: s.Config.AllowedOrigins,
    AllowMethods: []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
    AllowHeaders: []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
    AllowCredentials: true,
    MaxAge: 86400, // 24 hours
}))
```

### Security Headers
```go
// Security middleware
s.server.Use(middleware.SecureWithConfig(middleware.SecureConfig{
    XSSProtection:         "1; mode=block",
    ContentTypeNosniff:    "nosniff",
    XFrameOptions:         "DENY",
    HSTSMaxAge:            31536000,
    ContentSecurityPolicy: "default-src 'self'",
}))
```

## Deployment Implementation

### Build Process
```makefile
# Build the application
build:
	go build -o bin/telescope main.go

# Run the server
run:
	go run main.go server

# Run migrations
migrate: migrate-up

# Run migrations up
migrate-up:
	go run main.go migrate up
```

### Docker Support
```dockerfile
# Multi-stage build for production
FROM golang:1.24-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

EXPOSE 8080
CMD ["./main", "server"]
```

### Environment Configuration
```bash
# Production environment variables
export CONFIG_PATH=/etc/telescope/config.yaml
export SERVER_PORT=8080
export DATABASE_HOST=postgres.production
export DATABASE_PASSWORD=secure_password
export LOGGER_LEVEL=info
```

### Health Check Endpoint
```go
// Health check endpoint
func healthCheck(c echo.Context) error {
    return c.JSON(http.StatusOK, map[string]interface{}{
        "status": "healthy",
        "timestamp": time.Now().UTC(),
        "version": "1.0.0",
    })
}

// Add to routes
e.GET("/health", healthCheck)
```

This technical specification provides detailed implementation guidance for developers working on the Telescope Backend project, complementing the high-level architecture documentation.
