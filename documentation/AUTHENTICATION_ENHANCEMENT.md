# Authentication System Enhancement

## Overview

This document describes the enhanced authentication system that supports login using either email OR username along with a hashed password. The implementation follows Go best practices, clean code principles, and maintains backward compatibility where possible.

## Features

### Enhanced Login Support
- **Flexible Authentication**: Users can log in using either their email address or username
- **Automatic Detection**: The system automatically determines whether the provided identifier is an email or username
- **Input Validation**: Comprehensive validation for both email and username formats
- **Security**: Maintains the same security standards as the original implementation

### Security Features
- **Bcrypt Password Hashing**: Secure password storage using bcrypt with default cost
- **Consistent Error Messages**: Prevents user enumeration by returning consistent error messages
- **Input Sanitization**: Automatic trimming and normalization of identifiers
- **Timing Attack Prevention**: Same response time regardless of identifier type

## API Changes

### LoginRequest Structure

**Before:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**After:**
```json
{
  "identifier": "<EMAIL>",  // Can be email or username
  "password": "password123"
}
```

### Validation Rules

#### Email Validation
- Must be a valid email format (RFC 5322 compliant)
- Maximum length: 254 characters
- Case-insensitive matching

#### Username Validation
- Length: 3-255 characters
- Must start and end with alphanumeric characters
- Allowed characters: letters, numbers, underscore (_), hyphen (-), dot (.)
- Cannot have consecutive special characters
- Case-insensitive matching

## Implementation Details

### Repository Layer

#### New Method: `GetUserByUsername`
```go
func (r *userRepository) GetUserByUsername(ctx context.Context, username string) (*entity.User, error)
```

- Retrieves user by username from the database
- Follows the same pattern as `GetUserByEmail`
- Includes soft delete check (`deleted_at IS NULL`)

### Service Layer

#### Enhanced `validateUserCredentials`
```go
func (s *authService) validateUserCredentials(ctx context.Context, request presentation.LoginRequest) (*entity.User, error)
```

**Process Flow:**
1. Validate identifier format using `helper.ValidateLoginIdentifier`
2. Sanitize identifier (trim whitespace, convert to lowercase)
3. Determine identifier type (email or username)
4. Retrieve user using appropriate repository method
5. Verify password using bcrypt
6. Return user entity or error

#### New Helper Method: `getUserByIdentifier`
```go
func (s *authService) getUserByIdentifier(ctx context.Context, identifier, identifierType string) (*entity.User, error)
```

- Routes to appropriate repository method based on identifier type
- Centralizes user retrieval logic

### Validation Layer

#### New Validation Helper: `internal/helper/validation.go`

**Key Functions:**
- `IsValidUsername(username string) bool`: Validates username format
- `IsValidEmail(email string) bool`: Validates email format
- `IsEmailOrUsername(identifier string) string`: Determines identifier type
- `SanitizeIdentifier(identifier string) string`: Normalizes identifier
- `ValidateLoginIdentifier(identifier string) (string, error)`: Complete validation

## Usage Examples

### Login with Email
```go
request := presentation.LoginRequest{
    Identifier: "<EMAIL>",
    Password:   "securepassword123",
}

response, err := authService.Login(ctx, request)
```

### Login with Username
```go
request := presentation.LoginRequest{
    Identifier: "johndoe",
    Password:   "securepassword123",
}

response, err := authService.Login(ctx, request)
```

## Error Handling

### Validation Errors
- Invalid identifier format
- Identifier too short/long
- Invalid characters in username

### Authentication Errors
- User not found
- Invalid password
- Account disabled/deleted

**Note:** All authentication errors return the same generic message ("invalid credentials") to prevent user enumeration attacks.

## Testing

### Test Coverage
- Unit tests for validation functions
- Integration tests for authentication service
- Mock-based testing for repository interactions
- Edge case testing for security scenarios

### Test Files
- `internal/helper/validation_test.go`: Validation function tests
- `internal/service/auth/service_test.go`: Authentication service tests

## Migration Guide

### For API Clients
1. Update login requests to use `identifier` field instead of `email`
2. The `identifier` field accepts both email addresses and usernames
3. Existing email-based authentication continues to work

### For Developers
1. Use the new validation helpers for consistent identifier validation
2. Follow the established patterns for error handling
3. Maintain security best practices in any extensions

## Security Considerations

### Password Security
- Passwords are hashed using bcrypt with default cost (currently 10)
- Password verification uses constant-time comparison
- No password hints or recovery through this endpoint

### Input Validation
- All identifiers are validated before database queries
- SQL injection prevention through parameterized queries
- Input sanitization prevents various attack vectors

### Error Handling
- Consistent error messages prevent user enumeration
- Detailed logging for security monitoring
- Rate limiting should be implemented at the handler level

## Performance Considerations

### Database Queries
- Both email and username fields have database indexes
- Query performance is equivalent for both identifier types
- Soft delete checks are included in all queries

### Validation Performance
- Regex compilation is done once at package initialization
- Validation functions are optimized for common cases
- Minimal overhead for identifier type detection

## Future Enhancements

### Potential Improvements
1. **Multi-factor Authentication**: Add support for 2FA
2. **Account Lockout**: Implement account lockout after failed attempts
3. **Password Complexity**: Add configurable password complexity requirements
4. **Audit Logging**: Enhanced logging for security events
5. **Rate Limiting**: Built-in rate limiting for authentication attempts

### Backward Compatibility
The current implementation maintains backward compatibility with existing email-based authentication while adding username support. Future enhancements should continue to follow this principle.
