# My Understanding of Goose Migration Tool

## Based on What I've Seen in This Codebase

### File Format
Goose uses single SQL files with special annotations, not separate up/down files:

```sql
-- +goose Up
-- +goose StatementBegin
CREATE TABLE example (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS example;
-- +goose StatementEnd
```

### Key Patterns I've Observed
1. **Single file per migration** with both up and down sections
2. **Sequential numbering**: `001_`, `002_`, etc.
3. **StatementBegin/StatementEnd** for complex SQL blocks
4. **Up section** defines forward migration
5. **Down section** defines rollback migration

### Project-Specific Implementation
- Migration tool built at `./target/migration`
- Uses PostgreSQL exclusively via pgx driver
- Environment variable `DATABASE_URL` for connection
- Migration files in `./migrations/` directory
- Built via `make build-migration` command

### Commands Available
- `up` - Apply pending migrations
- `down` - Rollback one migration
- `status` - Show current state
- `version` - Show current version
- `create name` - Generate new migration file

### What I Need to Fix
My earlier attempt was wrong - I created separate `.up.sql` and `.down.sql` files when the project uses Goose's single-file format with annotations. I should have followed the existing `001_create_hello_world_table.sql` pattern.

This understanding is based solely on the documentation and example migration file I found in this codebase.
