# Currency Rate Fetching & Scheduling Design

## Overview

Design for external currency rate fetching service with periodic updates, caching, and fallback strategies for the cross-border money transfer system.

## Architecture

### 1. External Rate Provider Interface

```go
type ExternalRateProvider interface {
    GetRate(ctx context.Context, from, to string) (*RateResponse, error)
    GetMultipleRates(ctx context.Context, pairs []CurrencyPair) (map[string]*RateResponse, error)
    IsSupported(from, to string) bool
    HealthCheck(ctx context.Context) error
    GetProviderName() string
}

type RateResponse struct {
    Rate         decimal.Decimal `json:"rate"`
    FromCurrency string         `json:"from_currency"`
    ToCurrency   string         `json:"to_currency"`
    Timestamp    time.Time      `json:"timestamp"`
    Source       string         `json:"source"`
    ValidUntil   *time.Time     `json:"valid_until,omitempty"`
}

type CurrencyPair struct {
    From string `json:"from"`
    To   string `json:"to"`
}
```

### 2. Rate Fetcher Service

```go
type RateFetcherService struct {
    providers        []ExternalRateProvider
    repository       repository.ExchangeRateRepository
    cache           cache.CacheRepository
    primaryProvider  ExternalRateProvider
    logger          *slog.Logger
    config          *config.RateFetcherConfig
}

type RateFetcherConfig struct {
    FetchInterval     time.Duration `yaml:"fetch_interval"`     // 15m
    RetryAttempts     int          `yaml:"retry_attempts"`     // 3
    RetryDelay        time.Duration `yaml:"retry_delay"`        // 30s
    HealthCheckInterval time.Duration `yaml:"health_check_interval"` // 5m
    FallbackEnabled   bool         `yaml:"fallback_enabled"`   // true
    MaxStaleAge       time.Duration `yaml:"max_stale_age"`      // 1h
}
```

### 3. Scheduling Strategy

#### Primary Scheduler (Cron-based)

```go
type RateScheduler struct {
    fetcher    *RateFetcherService
    scheduler  *cron.Cron
    config     *config.RateSchedulerConfig
    logger     *slog.Logger
    ctx        context.Context
    cancel     context.CancelFunc
}

type RateSchedulerConfig struct {
    EnabledPairs     []CurrencyPair `yaml:"enabled_pairs"`
    FetchSchedule    string        `yaml:"fetch_schedule"`    // "*/15 * * * *" (every 15 minutes)
    HealthSchedule   string        `yaml:"health_schedule"`   // "*/5 * * * *" (every 5 minutes)
    BatchSize        int           `yaml:"batch_size"`        // 10
    TimeoutPerBatch  time.Duration `yaml:"timeout_per_batch"` // 30s
}
```

#### Background Worker Implementation

```go
func (s *RateScheduler) Start() error {
    s.ctx, s.cancel = context.WithCancel(context.Background())
    s.scheduler = cron.New(cron.WithSeconds())

    // Schedule rate fetching
    _, err := s.scheduler.AddFunc(s.config.FetchSchedule, func() {
        if err := s.fetchAllRates(); err != nil {
            s.logger.Error("Failed to fetch rates", "error", err)
        }
    })
    if err != nil {
        return fmt.Errorf("failed to schedule rate fetching: %w", err)
    }

    // Schedule health checks
    _, err = s.scheduler.AddFunc(s.config.HealthSchedule, func() {
        s.performHealthChecks()
    })
    if err != nil {
        return fmt.Errorf("failed to schedule health checks: %w", err)
    }

    s.scheduler.Start()
    s.logger.Info("Rate scheduler started")
    return nil
}

func (s *RateScheduler) Stop() {
    if s.cancel != nil {
        s.cancel()
    }
    if s.scheduler != nil {
        s.scheduler.Stop()
    }
    s.logger.Info("Rate scheduler stopped")
}
```

## Fetching Logic

### 1. Batch Fetching with Circuit Breaker

```go
func (r *RateFetcherService) FetchRatesForPairs(ctx context.Context, pairs []CurrencyPair) error {
    // Group pairs by batches
    batches := r.createBatches(pairs, r.config.BatchSize)

    for _, batch := range batches {
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
            if err := r.processBatch(ctx, batch); err != nil {
                r.logger.Error("Batch processing failed", "error", err, "batch_size", len(batch))
                // Continue with next batch, don't fail entire operation
            }
        }
    }

    return nil
}

func (r *RateFetcherService) processBatch(ctx context.Context, pairs []CurrencyPair) error {
    timeoutCtx, cancel := context.WithTimeout(ctx, r.config.TimeoutPerBatch)
    defer cancel()

    rates, err := r.primaryProvider.GetMultipleRates(timeoutCtx, pairs)
    if err != nil {
        return r.handleFetchError(timeoutCtx, pairs, err)
    }

    return r.saveRates(timeoutCtx, rates)
}
```

### 2. Fallback Strategy

```go
func (r *RateFetcherService) handleFetchError(ctx context.Context, pairs []CurrencyPair, err error) error {
    r.logger.Warn("Primary provider failed", "error", err, "pairs", len(pairs))

    // Try fallback providers
    for _, provider := range r.providers[1:] { // Skip primary (index 0)
        if err := provider.HealthCheck(ctx); err != nil {
            continue // Try next provider
        }

        rates, err := provider.GetMultipleRates(ctx, pairs)
        if err == nil {
            r.logger.Info("Fallback provider succeeded", "provider", provider.GetProviderName())
            return r.saveRates(ctx, rates)
        }
        r.logger.Warn("Fallback provider failed", "provider", provider.GetProviderName(), "error", err)
    }

    // All providers failed, use cached rates if available and not too stale
    return r.useCachedRatesIfValid(ctx, pairs)
}

func (r *RateFetcherService) useCachedRatesIfValid(ctx context.Context, pairs []CurrencyPair) error {
    staleThreshold := time.Now().Add(-r.config.MaxStaleAge)

    for _, pair := range pairs {
        cachedRate, err := r.cache.GetExchangeRate(ctx, pair.From, pair.To)
        if err != nil || cachedRate.Timestamp.Before(staleThreshold) {
            r.logger.Error("No valid cached rate available",
                "pair", fmt.Sprintf("%s-%s", pair.From, pair.To),
                "cache_age", time.Since(cachedRate.Timestamp))
            continue
        }

        r.logger.Info("Using cached rate", "pair", fmt.Sprintf("%s-%s", pair.From, pair.To))
    }

    return nil
}
```

### 3. Rate Persistence

```go
func (r *RateFetcherService) saveRates(ctx context.Context, rates map[string]*RateResponse) error {
    tx, err := r.repository.BeginTransaction(ctx)
    if err != nil {
        return fmt.Errorf("failed to begin transaction: %w", err)
    }
    defer tx.Rollback()

    for pairKey, rate := range rates {
        exchangeRate := &entity.ExchangeRate{
            CurrencyPairID: r.getCurrencyPairID(rate.FromCurrency, rate.ToCurrency),
            Rate:          rate.Rate,
            Source:        rate.Source,
            ValidFrom:     rate.Timestamp,
            ValidUntil:    rate.ValidUntil,
            IsActive:      true,
            SourceMetadata: map[string]interface{}{
                "provider":   rate.Source,
                "fetched_at": time.Now(),
            },
        }

        // Save to database
        if err := r.repository.CreateExchangeRate(ctx, tx, exchangeRate); err != nil {
            return fmt.Errorf("failed to save rate %s: %w", pairKey, err)
        }

        // Update cache
        if err := r.cache.SetExchangeRate(ctx, pairKey, rate, 24*time.Hour); err != nil {
            r.logger.Warn("Failed to cache rate", "pair", pairKey, "error", err)
            // Don't fail the entire operation for cache issues
        }

        // Trigger cache invalidation for related endpoints
        r.invalidateRelatedCache(ctx, rate.FromCurrency, rate.ToCurrency)
    }

    return tx.Commit()
}
```

## Provider Implementations

### 1. Example Provider Structure

```go
type FixerProvider struct {
    client    *http.Client
    apiKey    string
    baseURL   string
    timeout   time.Duration
    logger    *slog.Logger
}

func (f *FixerProvider) GetMultipleRates(ctx context.Context, pairs []CurrencyPair) (map[string]*RateResponse, error) {
    // Group by base currency for efficient API calls
    groupedPairs := f.groupByBaseCurrency(pairs)
    results := make(map[string]*RateResponse)

    for baseCurrency, targetCurrencies := range groupedPairs {
        rates, err := f.fetchRatesFromAPI(ctx, baseCurrency, targetCurrencies)
        if err != nil {
            return nil, fmt.Errorf("failed to fetch rates for base %s: %w", baseCurrency, err)
        }

        for k, v := range rates {
            results[k] = v
        }
    }

    return results, nil
}

func (f *FixerProvider) fetchRatesFromAPI(ctx context.Context, base string, targets []string) (map[string]*RateResponse, error) {
    url := fmt.Sprintf("%s/latest?access_key=%s&base=%s&symbols=%s",
        f.baseURL, f.apiKey, base, strings.Join(targets, ","))

    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return nil, err
    }

    resp, err := f.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var apiResponse struct {
        Success bool               `json:"success"`
        Rates   map[string]float64 `json:"rates"`
        Date    string            `json:"date"`
        Error   struct {
            Code string `json:"code"`
            Type string `json:"type"`
        } `json:"error"`
    }

    if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
        return nil, fmt.Errorf("failed to decode response: %w", err)
    }

    if !apiResponse.Success {
        return nil, fmt.Errorf("API error: %s - %s", apiResponse.Error.Code, apiResponse.Error.Type)
    }

    results := make(map[string]*RateResponse)
    timestamp := time.Now()

    for target, rate := range apiResponse.Rates {
        pairKey := fmt.Sprintf("%s-%s", base, target)
        results[pairKey] = &RateResponse{
            Rate:         decimal.NewFromFloat(rate),
            FromCurrency: base,
            ToCurrency:   target,
            Timestamp:    timestamp,
            Source:       f.GetProviderName(),
        }
    }

    return results, nil
}
```

## Configuration

ensure the configuration is stored on multiple place: database, and files. treat database as the highest priority.

### Rate Fetcher Configuration

```yaml
rate_fetcher:
  providers:
    - name: "fixer"
      api_key: "${FIXER_API_KEY}"
      base_url: "http://data.fixer.io/api"
      timeout: "30s"
      enabled: true
      priority: 1
    - name: "exchangerate-api"
      api_key: "${EXCHANGE_API_KEY}"
      base_url: "https://v6.exchangerate-api.com/v6"
      timeout: "25s"
      enabled: true
      priority: 2

  fetch_interval: "15m"
  retry_attempts: 3
  retry_delay: "30s"
  health_check_interval: "5m"
  fallback_enabled: true
  max_stale_age: "1h"
  batch_size: 10
  timeout_per_batch: "30s"

scheduler:
  enabled_pairs:
    - { from: "USD", to: "IDR" }
    - { from: "USD", to: "MYR" }
    - { from: "MYR", to: "IDR" }
    - { from: "IDR", to: "USD" }
    - { from: "MYR", to: "USD" }
    - { from: "IDR", to: "MYR" }

  fetch_schedule: "*/15 * * * *" # Every 15 minutes
  health_schedule: "*/5 * * * *" # Every 5 minutes
```

## Monitoring & Alerts

### Prometheus Metrics

```go
var (
    rateFetchDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "currency_rate_fetch_duration_seconds",
            Help: "Duration of currency rate fetching operations",
        },
        []string{"provider", "status"},
    )

    rateFetchErrors = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "currency_rate_fetch_errors_total",
            Help: "Total number of currency rate fetch errors",
        },
        []string{"provider", "error_type"},
    )

    cachedRatesUsed = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "currency_cached_rates_used_total",
            Help: "Total number of times cached rates were used",
        },
        []string{"pair", "reason"},
    )
)
```

## Integration with Uber FX

```go
// In internal/fx/module.go
var RateModule = fx.Options(
    fx.Provide(NewFixerProvider),
    fx.Provide(NewExchangeRateAPIProvider),
    fx.Provide(NewRateFetcherService),
    fx.Provide(NewRateScheduler),
)

// Lifecycle hooks
func NewRateScheduler(
    lc fx.Lifecycle,
    fetcher *RateFetcherService,
    config *config.RateSchedulerConfig,
    logger *slog.Logger,
) *RateScheduler {
    scheduler := &RateScheduler{
        fetcher: fetcher,
        config:  config,
        logger:  logger,
    }

    lc.Append(fx.Hook{
        OnStart: func(ctx context.Context) error {
            return scheduler.Start()
        },
        OnStop: func(ctx context.Context) error {
            scheduler.Stop()
            return nil
        },
    })

    return scheduler
}
```

## Recommended Scheduling

### Production Recommendations

1. **Primary Schedule**: Every 15 minutes during business hours
2. **Off-hours Schedule**: Every 30 minutes during non-business hours
3. **Health Check**: Every 5 minutes
4. **Fallback Grace Period**: 1 hour for stale rates
5. **Circuit Breaker**: 5 failures before switching provider

### Development/Testing

1. **Primary Schedule**: Every 5 minutes for rapid testing
2. **Health Check**: Every 2 minutes
3. **Fallback Grace Period**: 30 minutes
4. **Circuit Breaker**: 3 failures before switching

This design ensures reliable, efficient currency rate fetching with proper fallback strategies and monitoring capabilities.
