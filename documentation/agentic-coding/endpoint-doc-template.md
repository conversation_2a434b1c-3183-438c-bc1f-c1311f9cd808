# [ENDPOINT_NAME] API

> **Endpoint:** `[HTTP_METHOD] [ENDPOINT_PATH]`
> **Content-Type:** `[CONTENT_TYPE]`
> **Authentication:** [AUTH_REQUIREMENT]

## Overview

[BRIEF_DESCRIPTION_OF_ENDPOINT_PURPOSE_AND_FUNCTIONALITY]

---

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `[PARAM_NAME]` | `[TYPE]` | [Required/Optional] | [PARAM_DESCRIPTION] |
| `[PARAM_NAME_2]` | `[TYPE]` | [Required/Optional] | [PARAM_DESCRIPTION] |

### [SPECIAL_REQUIREMENTS_SECTION_IF_NEEDED]

> **[REQUIREMENT_TYPE]:** [REQUIREMENT_DETAILS]
> **[ADDITIONAL_INFO]:** [MORE_DETAILS]

---

## Authentication Headers

```http
[AUTH_HEADER_1]: <[PLACEHOLDER_VALUE]>
[AUTH_HEADER_2]: <[PLACEHOLDER_VALUE]>
[AUTH_HEADER_3]: <[PLACEHOLDER_VALUE]>
Content-Type: [CONTENT_TYPE]
```

> **Note:** [AUTHENTICATION_GUIDANCE_OR_REFERENCE]

---

## Example Request

### cURL Command

```bash
curl -X [HTTP_METHOD] '[FULL_ENDPOINT_URL]' \
  --header '[AUTH_HEADER_1]: [EXAMPLE_VALUE]' \
  --header '[AUTH_HEADER_2]: [EXAMPLE_VALUE]' \
  --header '[AUTH_HEADER_3]: [EXAMPLE_VALUE]' \
  [REQUEST_BODY_FORMAT_SPECIFIC_CURL_FLAGS]
```

### Request Payload Structure

```json
{
  "[FIELD_1]": "[EXAMPLE_VALUE_1]",
  "[FIELD_2]": "[EXAMPLE_VALUE_2]",
  "[FIELD_3]": "[EXAMPLE_VALUE_3]"
}
```

---

## Success Response

**Status Code:** `[SUCCESS_STATUS_CODE] [STATUS_TEXT]`

```json
{
  "statusCode": [SUCCESS_CODE],
  "message": "[SUCCESS_MESSAGE]",
  "data": {
    "[RESPONSE_FIELD_1]": "[EXAMPLE_VALUE_1]",
    "[RESPONSE_FIELD_2]": "[EXAMPLE_VALUE_2]",
    "[RESPONSE_FIELD_3]": "[EXAMPLE_VALUE_3]"
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `[FIELD_NAME]` | `[TYPE]` | [FIELD_DESCRIPTION] |
| `[FIELD_NAME_2]` | `[TYPE]` | [FIELD_DESCRIPTION] |
| `[FIELD_NAME_3]` | `[TYPE]` | [FIELD_DESCRIPTION] |

---

## Error Responses

### Validation Errors (400 Bad Request)

```json
{
  "statusCode": 400,
  "message": "[ERROR_MESSAGE]",
  "errors": [
    {
      "field": "[FIELD_NAME]",
      "message": "[ERROR_DESCRIPTION]"
    }
  ]
}
```

### Authentication Errors (401 Unauthorized)

```json
{
  "statusCode": 401,
  "message": "[AUTH_ERROR_MESSAGE]",
  "error": "[ERROR_CODE]"
}
```

### Authorization Errors (403 Forbidden)

```json
{
  "statusCode": 403,
  "message": "[PERMISSION_ERROR_MESSAGE]",
  "error": "[ERROR_CODE]"
}
```

### Not Found Errors (404 Not Found)

```json
{
  "statusCode": 404,
  "message": "[RESOURCE_NOT_FOUND_MESSAGE]",
  "error": "[ERROR_CODE]"
}
```

### Server Errors (500 Internal Server Error)

```json
{
  "statusCode": 500,
  "message": "[SERVER_ERROR_MESSAGE]",
  "error": "[ERROR_DESCRIPTION]"
}
```

---

## Next Steps

After successful [OPERATION_NAME]:

1. **[STEP_1_TITLE]** - [STEP_1_DESCRIPTION]
2. **[STEP_2_TITLE]** - [STEP_2_DESCRIPTION]
3. **[STEP_3_TITLE]** - [STEP_3_DESCRIPTION]
4. **[STEP_4_TITLE]** - [STEP_4_DESCRIPTION]

---

## Important Notes

- **[IMPORTANT_ASPECT_1]:** [IMPORTANT_DETAIL_1]
- **[IMPORTANT_ASPECT_2]:** [IMPORTANT_DETAIL_2]
- **[IMPORTANT_ASPECT_3]:** [IMPORTANT_DETAIL_3]
- **Support:** Contact [SUPPORT_EMAIL] for assistance

---

## Related Endpoints

- [Related Endpoint 1](./[RELATED_FILE_1].md) - [BRIEF_DESCRIPTION]
- [Related Endpoint 2](./[RELATED_FILE_2].md) - [BRIEF_DESCRIPTION]
- [Related Endpoint 3](./[RELATED_FILE_3].md) - [BRIEF_DESCRIPTION]
- [Related Endpoint 4](./[RELATED_FILE_4].md) - [BRIEF_DESCRIPTION]

---

## Template Usage Instructions

Replace all placeholders in `[BRACKETS]` with actual values:

### Basic Information
- `[ENDPOINT_NAME]` - Human-readable name for the endpoint
- `[HTTP_METHOD]` - GET, POST, PUT, DELETE, etc.
- `[ENDPOINT_PATH]` - The URL path (e.g., `/api/v1/users`)
- `[CONTENT_TYPE]` - application/json, multipart/form-data, etc.
- `[AUTH_REQUIREMENT]` - Required, Optional, None

### Content Sections
- `[BRIEF_DESCRIPTION...]` - 1-2 sentences describing the endpoint purpose
- `[PARAM_NAME]`, `[TYPE]`, `[PARAM_DESCRIPTION]` - Parameter details
- `[AUTH_HEADER_X]` - Authentication header names
- `[EXAMPLE_VALUE_X]` - Realistic example values
- `[SUCCESS_STATUS_CODE]` - HTTP status code (200, 201, etc.)

### Customization Notes
- Remove unused error response sections if not applicable
- Adjust parameter table based on request type (query, body, path params)
- Modify cURL example based on content type (form data, JSON, etc.)
- Update "Next Steps" section based on typical workflow
- Link to actual related endpoints in your documentation
