# Refresh Token Functionality

## Implementation Complete

### Files Created/Updated:
- `migrations/0016_refresh_tokens.sql` - Database migration file
- `internal/entity/refresh_token.go` - Updated entity structure

## Database Design

### Table: `refresh_tokens`
Following DATABASE_RULES.md specifications:

```sql
CREATE TABLE refresh_tokens (
    id SERIAL PRIMARY KEY,              -- Internal ID
    external_id UUID NOT NULL UNIQUE,   -- External UUID v7 (backend generated)
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,   -- Hashed token (security)
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete
);
```

### Indexes Created:
- `idx_refresh_tokens_external_id` - API lookups
- `idx_refresh_tokens_user_id` - User token queries
- `idx_refresh_tokens_token_hash` - Token validation
- `idx_refresh_tokens_expires_at` - Expiration checks
- `idx_refresh_tokens_is_revoked` - Active token filtering
- `idx_refresh_tokens_deleted_at` - Soft delete queries

## Entity Structure

```go
type RefreshToken struct {
    ID         int        `json:"id" db:"id"`
    ExternalID uuid.UUID  `json:"external_id" db:"external_id"`
    UserID     int        `json:"user_id" db:"user_id"`
    TokenHash  string     `json:"token_hash" db:"token_hash"`
    ExpiresAt  time.Time  `json:"expires_at" db:"expires_at"`
    IsRevoked  bool       `json:"is_revoked" db:"is_revoked"`
    CreatedAt  time.Time  `json:"created_at" db:"created_at"`
    UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
    DeletedAt  *time.Time `json:"deleted_at" db:"deleted_at"`

    // Relations
    User *User `json:"user,omitempty"`
}
```

## Business Logic Implementation

### Key Features:
- **JWT Refresh Management**: Secure token storage and validation
- **User Association**: Proper foreign key relationship with users table
- **Security**: Token hash storage (never plaintext)
- **Expiration Control**: Precise timestamp-based expiration
- **Revocation Capability**: Manual token invalidation
- **Audit Trail**: Full timestamp tracking with soft delete
- **Referential Integrity**: CASCADE delete when user is removed

### Design Principles Applied:
- **KISS**: Simple, straightforward token management
- **YAGNI**: Only essential fields for refresh functionality
- **Security First**: Hash-based storage, proper indexing
- **Database Rules Compliance**: Double ID system, soft delete, timezone awareness

### Usage Pattern:
1. Generate refresh token → hash and store in `token_hash`
2. Associate with user via `user_id` foreign key
3. Set expiration via `expires_at`
4. Validate by checking hash, expiration, and revocation status
5. Revoke by setting `is_revoked = true`
6. Clean up via soft delete (`deleted_at`)
