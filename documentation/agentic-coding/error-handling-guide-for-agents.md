# Error Handling Guide for Agentic Coding

## Overview

This document provides comprehensive guidelines for implementing consistent, secure, and user-friendly error handling in the telescope-be codebase. Follow YAGNI (You Aren't Gonna Need It) principle - implement only what's immediately needed.

## Core Principles

1. **Consistency**: Use existing `appctx.ResponseBuilder` pattern
2. **Security**: Never expose internal system details
3. **User Experience**: Provide actionable error messages
4. **Observability**: Log errors with sufficient context
5. **Simplicity**: Keep error handling straightforward

## Existing Error Response Structure

The codebase uses a standardized response structure:

```go
type Response struct {
    Code    int      `json:"code"`
    Message string   `json:"message"`
    Data    any      `json:"data"`
    Error   string   `json:"error,omitempty"`
    Errors  []string `json:"errors,omitempty"`
    Meta    any      `json:"meta,omitempty"`
}
```

## Error Code Categories

### HTTP Status Code Mapping

| Category | HTTP Status | Code Range | Use Case |
|----------|------------|------------|----------|
| **Success** | 2xx | 2000-2999 | Successful operations |
| **Client Errors** | 4xx | 4000-4999 | Invalid requests, validation |
| **Server Errors** | 5xx | 5000-5999 | Internal system errors |

### Detailed Error Codes

#### 2xxx - Success Codes
```go
const (
    CodeSuccess           = 2000 // General success
    CodeCreated          = 2001 // Resource created successfully
    CodeUpdated          = 2002 // Resource updated successfully
    CodeDeleted          = 2003 // Resource deleted successfully
)
```

#### 4xxx - Client Error Codes
```go
const (
    // General client errors
    CodeBadRequest       = 4000 // Invalid request format
    CodeValidationFailed = 4001 // Request validation failed
    CodeNotFound         = 4004 // Resource not found
    CodeConflict         = 4009 // Resource already exists

    // Authentication/Authorization
    CodeUnauthorized     = 4010 // Invalid/missing authentication
    CodeForbidden        = 4013 // Insufficient permissions
    CodeTokenExpired     = 4014 // JWT token expired
    CodeInvalidToken     = 4015 // Malformed JWT token

    // Business logic errors
    CodeInvalidCurrency  = 4100 // Invalid currency code
    CodeInactiveCountry  = 4101 // Country not active
    CodeRateNotAvailable = 4102 // Exchange rate unavailable
    CodeAmountOutOfRange = 4103 // Transfer amount out of limits

    // Rate limiting
    CodeRateLimitExceeded = 4290 // Rate limit exceeded
)
```

#### 5xxx - Server Error Codes
```go
const (
    // General server errors
    CodeInternalError    = 5000 // General internal error
    CodeServiceUnavailable = 5003 // Service temporarily unavailable

    // Database errors
    CodeDatabaseError    = 5100 // Database operation failed
    CodeConnectionError  = 5101 // Database connection failed
    CodeTransactionError = 5102 // Transaction failed

    // External service errors
    CodeExternalAPIError = 5200 // External API error
    CodeRateProviderError = 5201 // Currency rate provider error
    CodeCacheError       = 5202 // Cache operation error
)
```

## Implementation Patterns

### 1. Service Layer Error Handling

```go
func (s *countryService) CreateCountry(ctx context.Context, req *presentation.CountryCreateRequest) (*presentation.CountryResponse, error) {
    // Validate business rules
    if exists, err := s.repository.CountryExistsByCode(ctx, req.Code); err != nil {
        s.logger.Error("Failed to check country existence",
            "code", req.Code, "error", err)
        return nil, NewInternalError("Failed to validate country")
    } else if exists {
        return nil, NewConflictError(fmt.Sprintf("Country with code %s already exists", req.Code))
    }

    // Create entity
    country := &entity.Country{
        Code:             req.Code,
        Name:             req.Name,
        FlagEmoji:        req.FlagEmoji,
        IsActive:         req.IsActive,
        SupportsReceiving: req.SupportsReceiving,
        SupportsSending:  req.SupportsSending,
        CreatedBy:        getUserID(ctx),
        UpdatedBy:        getUserID(ctx),
    }

    // Save to database
    savedCountry, err := s.repository.CreateCountry(ctx, country)
    if err != nil {
        s.logger.Error("Failed to create country",
            "country", country, "error", err)
        return nil, NewInternalError("Failed to create country")
    }

    // Convert to response
    return s.mapToResponse(savedCountry), nil
}
```

### 2. Handler Layer Error Handling

```go
func (h *countryHandler) CreateCountry(c echo.Context) error {
    var req presentation.CountryCreateRequest

    // Bind and validate request
    if err := c.Bind(&req); err != nil {
        return appctx.NewResponseBuilder().
            WithCode(CodeBadRequest).
            WithMessage("Invalid request format").
            WithError("Request binding failed").
            WithHTTPStatus(http.StatusBadRequest).
            JSON(c)
    }

    if err := c.Validate(&req); err != nil {
        return h.handleValidationError(c, err)
    }

    // Call service
    country, err := h.service.CreateCountry(c.Request().Context(), &req)
    if err != nil {
        return h.handleServiceError(c, err)
    }

    // Success response
    return appctx.NewResponseBuilder().
        WithCode(CodeCreated).
        WithMessage("Country created successfully").
        WithData(country).
        WithHTTPStatus(http.StatusCreated).
        JSON(c)
}

func (h *countryHandler) handleServiceError(c echo.Context, err error) error {
    switch e := err.(type) {
    case *ConflictError:
        return appctx.NewResponseBuilder().
            WithCode(CodeConflict).
            WithMessage(e.Message).
            WithHTTPStatus(http.StatusConflict).
            JSON(c)
    case *NotFoundError:
        return appctx.NewResponseBuilder().
            WithCode(CodeNotFound).
            WithMessage(e.Message).
            WithHTTPStatus(http.StatusNotFound).
            JSON(c)
    case *ValidationError:
        return appctx.NewResponseBuilder().
            WithCode(CodeValidationFailed).
            WithMessage("Validation failed").
            WithErrors(e.Errors).
            WithHTTPStatus(http.StatusBadRequest).
            JSON(c)
    default:
        h.logger.Error("Unexpected service error", "error", err)
        return appctx.NewResponseBuilder().
            WithCode(CodeInternalError).
            WithMessage("An internal error occurred").
            WithHTTPStatus(http.StatusInternalServerError).
            JSON(c)
    }
}

func (h *countryHandler) handleValidationError(c echo.Context, err error) error {
    var validationErrors []string

    if ve, ok := err.(validator.ValidationErrors); ok {
        for _, e := range ve {
            switch e.Tag() {
            case "required":
                validationErrors = append(validationErrors,
                    fmt.Sprintf("%s is required", e.Field()))
            case "len":
                validationErrors = append(validationErrors,
                    fmt.Sprintf("%s must be exactly %s characters", e.Field(), e.Param()))
            case "min":
                validationErrors = append(validationErrors,
                    fmt.Sprintf("%s must be at least %s characters", e.Field(), e.Param()))
            case "max":
                validationErrors = append(validationErrors,
                    fmt.Sprintf("%s must be at most %s characters", e.Field(), e.Param()))
            default:
                validationErrors = append(validationErrors,
                    fmt.Sprintf("%s is invalid", e.Field()))
            }
        }
    } else {
        validationErrors = append(validationErrors, "Invalid request data")
    }

    return appctx.NewResponseBuilder().
        WithCode(CodeValidationFailed).
        WithMessage("Request validation failed").
        WithErrors(validationErrors).
        WithHTTPStatus(http.StatusBadRequest).
        JSON(c)
}
```

### 3. Custom Error Types

```go
// Custom error types for better error handling
type AppError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Cause   error  `json:"-"`
}

func (e *AppError) Error() string {
    return e.Message
}

func (e *AppError) Unwrap() error {
    return e.Cause
}

// Specific error constructors
func NewValidationError(message string) *AppError {
    return &AppError{
        Code:    CodeValidationFailed,
        Message: message,
    }
}

func NewNotFoundError(message string) *AppError {
    return &AppError{
        Code:    CodeNotFound,
        Message: message,
    }
}

func NewConflictError(message string) *AppError {
    return &AppError{
        Code:    CodeConflict,
        Message: message,
    }
}

func NewInternalError(message string) *AppError {
    return &AppError{
        Code:    CodeInternalError,
        Message: message,
    }
}

// Validation error with multiple messages
type ValidationError struct {
    *AppError
    Errors []string
}

func NewValidationErrorWithDetails(errors []string) *ValidationError {
    return &ValidationError{
        AppError: &AppError{
            Code:    CodeValidationFailed,
            Message: "Validation failed",
        },
        Errors: errors,
    }
}
```

## Repository Layer Error Handling

```go
func (r *countryRepository) CreateCountry(ctx context.Context, country *entity.Country) (*entity.Country, error) {
    query := `
        INSERT INTO countries (code, name, flag_emoji, is_active, supports_sending, supports_receiving, created_by, updated_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, external_id, created_at, updated_at, version
    `

    err := r.db.QueryRow(ctx, query,
        country.Code, country.Name, country.FlagEmoji,
        country.IsActive, country.SupportsSending, country.SupportsReceiving,
        country.CreatedBy, country.UpdatedBy,
    ).Scan(&country.ID, &country.ExternalID, &country.CreatedAt, &country.UpdatedAt, &country.Version)

    if err != nil {
        if pgErr, ok := err.(*pgconn.PgError); ok {
            switch pgErr.Code {
            case "23505": // unique_violation
                if strings.Contains(pgErr.ConstraintName, "code") {
                    return nil, NewConflictError(fmt.Sprintf("Country with code %s already exists", country.Code))
                }
                return nil, NewConflictError("Country already exists")
            case "23503": // foreign_key_violation
                return nil, NewValidationError("Invalid reference data")
            default:
                r.logger.Error("Database error creating country",
                    "code", pgErr.Code, "message", pgErr.Message, "error", err)
            }
        }

        return nil, NewInternalError("Failed to create country")
    }

    return country, nil
}
```

## Security Considerations

### 1. Error Message Sanitization

```go
// GOOD: Safe error messages
func (s *userService) GetUserByEmail(ctx context.Context, email string) (*entity.User, error) {
    user, err := s.repository.GetUserByEmail(ctx, email)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            // Don't reveal whether email exists
            return nil, NewNotFoundError("Invalid credentials")
        }
        return nil, NewInternalError("Authentication failed")
    }
    return user, nil
}

// BAD: Leaks information
func (s *userService) GetUserByEmailBad(ctx context.Context, email string) (*entity.User, error) {
    user, err := s.repository.GetUserByEmail(ctx, email)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            // This reveals whether the email exists in the system
            return nil, NewNotFoundError(fmt.Sprintf("User with email %s not found", email))
        }
        return nil, err // This might leak database connection strings or schemas
    }
    return user, nil
}
```

### 2. Rate Limiting Error Handling

```go
func (h *Handler) handleRateLimit(c echo.Context) error {
    return appctx.NewResponseBuilder().
        WithCode(CodeRateLimitExceeded).
        WithMessage("Too many requests. Please try again later.").
        WithHTTPStatus(http.StatusTooManyRequests).
        WithMeta(map[string]interface{}{
            "retry_after": "60", // seconds
        }).
        JSON(c)
}
```

## Logging Strategy

```go
func (s *service) processWithLogging(ctx context.Context, operation string, data interface{}) error {
    start := time.Now()

    defer func() {
        duration := time.Since(start)
        s.logger.Info("Operation completed",
            "operation", operation,
            "duration_ms", duration.Milliseconds(),
        )
    }()

    if err := s.doProcess(ctx, data); err != nil {
        // Log errors with correlation ID for tracing
        s.logger.Error("Operation failed",
            "operation", operation,
            "error", err.Error(),
            "correlation_id", getCorrelationID(ctx),
            "user_id", getUserID(ctx),
        )
        return err
    }

    return nil
}
```

## Adding New Error Codes

### Step-by-Step Process

1. **Define the error code** in the appropriate range:
   ```go
   const CodeNewBusinessError = 4150 // New business logic error
   ```

2. **Create error constructor**:
   ```go
   func NewBusinessError(message string) *AppError {
       return &AppError{
           Code:    CodeNewBusinessError,
           Message: message,
       }
   }
   ```

3. **Add to handler error mapping**:
   ```go
   case *BusinessError:
       return appctx.NewResponseBuilder().
           WithCode(CodeNewBusinessError).
           WithMessage(e.Message).
           WithHTTPStatus(http.StatusBadRequest).
           JSON(c)
   ```

4. **Document in API spec** (if applicable)

### Error Code Registry

Maintain a simple registry to avoid conflicts:

```go
// errors/registry.go
var ErrorCodeRegistry = map[int]string{
    4000: "Bad Request",
    4001: "Validation Failed",
    4004: "Not Found",
    4009: "Conflict",
    4100: "Invalid Currency",
    4101: "Inactive Country",
    // ... add new codes here
}

func RegisterErrorCode(code int, description string) {
    if _, exists := ErrorCodeRegistry[code]; exists {
        panic(fmt.Sprintf("Error code %d already exists", code))
    }
    ErrorCodeRegistry[code] = description
}
```

## Testing Error Handling

```go
func TestCreateCountry_Conflict(t *testing.T) {
    service := setupTestService(t)

    req := &presentation.CountryCreateRequest{
        Code: "USA",
        Name: "United States",
    }

    _, err := service.CreateCountry(context.Background(), req)

    // Assert specific error type
    var conflictErr *ConflictError
    require.True(t, errors.As(err, &conflictErr))
    assert.Equal(t, CodeConflict, conflictErr.Code)
    assert.Contains(t, conflictErr.Message, "USA")
}
```

## Monitoring and Metrics

```go
var (
    errorCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_errors_total",
            Help: "Total number of HTTP errors",
        },
        []string{"code", "method", "endpoint"},
    )
)

func (h *Handler) recordError(code int, method, endpoint string) {
    errorCounter.WithLabelValues(
        strconv.Itoa(code),
        method,
        endpoint,
    ).Inc()
}
```

## Summary

- **Use existing ResponseBuilder pattern**
- **Follow error code ranges and meanings**
- **Keep error messages user-friendly and secure**
- **Log errors with sufficient context**
- **Test error scenarios thoroughly**
- **Don't over-engineer - implement what you need**

Remember: Good error handling improves user experience and system maintainability. When in doubt, follow the existing patterns in the codebase.
