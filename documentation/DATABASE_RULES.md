# Database Design rules

## ID

each table should have internal and external id.
- internal ID is integer auto-increment
- external ID is UUID v7, generated by backend not by postgresql.

### Rule
- both ID should be indexed
- always use external ID on the presentation layer

## Soft Delete

implement soft delete mechanism using `deleted_at` with data type `TIM<PERSON><PERSON>MP WITH TIME ZONE`.

this column should be indexed

## Timezone Aware data type

every column that store date or datetime should be aware of the timezone. storing data to tables still retains the timezone information.

## Table naming

- use plural. eg: `banks`, `accounts`, `profiles`, `countries`.

## Column naming

- things that has time info should be ended with `_at`. example: `created_at`, `updated_at`, `deleted_at`
- columns that has reference to another table should begin with singular table name, and ended with `_id`. example: `bank_id`, `user_id`, `country_id`
