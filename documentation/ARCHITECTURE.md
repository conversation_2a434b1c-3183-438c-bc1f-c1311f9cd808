# Telescope Backend - Architecture Documentation

## Overview

Telescope Backend is a Go-based HTTP server built with a clean, layered architecture that emphasizes separation of concerns, maintainability, and scalability. The application follows industry best practices for Go development and provides a solid foundation for building robust web services.

### Key Features
- **Clean Architecture**: Separation of concerns with distinct layers
- **HTTP Server**: Built with Echo framework for high performance
- **Database Migrations**: Using Goose for version-controlled schema management
- **Configuration Management**: YAML-based configuration with environment variable support
- **Graceful Shutdown**: Proper signal handling and resource cleanup
- **CORS Support**: Configurable cross-origin resource sharing
- **CLI Interface**: Cobra-based command-line interface

## Architecture Patterns

### 1. Clean Architecture
The project follows Clean Architecture principles with clear boundaries between layers:

```
┌─────────────────────────────────────────────────────────────┐
│                    External Interfaces                      │
│  (HTTP Handlers, CLI Commands, Database Connections)        │
├─────────────────────────────────────────────────────────────┤
│                    Application Layer                        │
│  (Use Cases, Business Logic, Services)                      │
├─────────────────────────────────────────────────────────────┤
│                    Domain Layer                             │
│  (Entities, Business Rules, Interfaces)                     │
├─────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                     │
│  (Database, External APIs, File System)                     │
└─────────────────────────────────────────────────────────────┘
```

### 2. Dependency Inversion
- High-level modules don't depend on low-level modules
- Both depend on abstractions
- Abstractions don't depend on details

### 3. Interface Segregation
- Small, focused interfaces
- Clients depend only on methods they use
- Clear contract definitions

## Project Structure

```
telescope-be/
├── cmd/                    # Application entry points and commands
│   ├── migration.go       # Database migration commands
│   └── server.go          # HTTP server commands
├── configs/               # Configuration files
│   └── config.yaml        # Main configuration file
├── deployment/            # Deployment configurations
├── documentation/         # Project documentation
├── internal/              # Private application code
│   ├── appctx/           # Application context and configuration
│   ├── constants/        # Application constants
│   ├── entity/           # Domain entities
│   ├── handler/          # HTTP request handlers
│   ├── middleware/       # HTTP middleware
│   ├── presentation/     # Request/Response DTOs
│   ├── repository/       # Data access layer
│   ├── routes/           # HTTP routing configuration
│   ├── server/           # HTTP server implementation
│   └── service/          # Business logic layer
├── migrations/            # Database migration files
├── pkg/                  # Public packages (if any)
├── go.mod                # Go module definition
├── go.sum                # Go module checksums
├── main.go               # Application entry point
├── Makefile              # Build and development commands
└── README.md             # Project overview
```

## Layered Architecture

### 1. Presentation Layer (`internal/handler/`)
**Purpose**: Handle HTTP requests and responses, input validation, and request routing.

**Components**:
- Handles HTTP operations
- Request/response transformation
- Input validation
- HTTP status code management

**Responsibilities**:
- Parse incoming HTTP requests
- Validate request data
- Call appropriate service methods
- Format and return HTTP responses
- Handle HTTP-specific errors

### 2. Service Layer (`internal/service/`)
**Purpose**: Implement business logic, orchestrate operations, and enforce business rules.

**Components**:
- Business logic operations
- Transaction management
- Business rule validation
- External service integration

**Responsibilities**:
- Implement business logic
- Coordinate between multiple repositories
- Handle business rule validation
- Manage transactions
- Integrate with external services

### 3. Repository Layer (`internal/repository/`)
**Purpose**: Abstract data access, provide data persistence, and handle data queries.

**Components**:
- Data access interfaces
- Database connection management
- Query optimization
- Data mapping

**Responsibilities**:
- Abstract data storage details
- Handle database connections
- Execute queries and transactions
- Map database results to entities
- Provide data access methods

### 4. Entity Layer (`internal/entity/`)
**Purpose**: Define domain models, business entities, and core business rules.

**Components**:
- `Duck`: Core duck entity with properties
- Business rule validation
- Domain-specific logic

**Responsibilities**:
- Define data structures
- Enforce business rules
- Provide domain-specific methods
- Maintain data integrity

### 5. Presentation DTOs (`internal/presentation/`)
**Purpose**: Define data transfer objects for API requests and responses.

**Components**:
- `DuckCreateRequest`: Input validation for duck creation
- `DuckCreateResponse`: Output format for duck creation
- `DuckGetResponse`: Output format for duck retrieval

**Responsibilities**:
- Define API contract
- Handle input validation
- Format output data
- Maintain API versioning

## Configuration Management

### Configuration Structure
The application uses a hierarchical configuration approach:

```yaml
server:
  host: "localhost"
  port: "8080"
  mode: "debug"
  allowed_origins: ["*"]

database:
  driver: "postgres"
  host: "localhost"
  port: "5432"
  user: "postgres"
  password: "password"
  database: "telescope"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

logger:
  level: "info"
  format: "json"

provider:
  idrx:
    base_url: "https://api.example.com"
    timeout: "30s"
```

### Configuration Loading Strategy
1. **Environment Variables**: Override configuration via environment variables
2. **Config Files**: YAML-based configuration files
3. **Default Values**: Sensible defaults for all configuration options
4. **Validation**: Configuration validation at startup

### Configuration Sources Priority
1. Command-line flags
2. Environment variables
3. Configuration files
4. Default values

## Dependency Injection

### Dependency Container
The application uses a simple dependency injection pattern:

```go
type Dependencies struct {
    HTTPHandler *http.HTTPHandler
}

type HTTPHandler struct {
    DuckHandler *duck.DuckHandler
}

type DuckHandler struct {
    service duck.DuckService
}
```

### Service Initialization
```go
func SetupRoutes(e *echo.Echo) {
    service := service.NewService(&service.Dependencies{})
    handler := http.NewHTTPHandler(&http.Dependencies{Service: service})
    dependencies := &Dependencies{HTTPHandler: handler}

    // Setup routes with dependencies
    SetupDuckRoutes(v1, dependencies)
}
```

## API Design

### RESTful Endpoints
```
GET    /api/v1/duck      - Retrieve duck information
POST   /api/v1/duck      - Create a new duck
```

### API Versioning
- URL-based versioning (`/api/v1/`)
- Backward compatibility support
- Deprecation strategy

### Request/Response Format
- **Content-Type**: `application/json`
- **Request Validation**: Required field validation
- **Response Format**: Consistent JSON structure
- **Error Handling**: Standardized error responses

### Example API Contract
```go
type DuckCreateRequest struct {
    Name  string `json:"name" validate:"required,min=1,max=100"`
    Breed string `json:"breed" validate:"required,min=1,max=50"`
    Sound string `json:"sound" validate:"required,min=1,max=50"`
}

type DuckCreateResponse struct {
    Name  string `json:"name"`
    Breed string `json:"breed"`
    Sound string `json:"sound"`
}
```

## Database Layer

### Migration Strategy
- **Tool**: Goose for database migrations
- **Version Control**: Sequential migration files
- **Rollback Support**: Down migrations for rollbacks
- **Environment Support**: Different configurations per environment

### Migration Commands
```bash
make migrate-up      # Apply all pending migrations
make migrate-down    # Rollback last migration
make migrate-status  # Check migration status
make migrate-reset   # Reset all migrations
```

### Database Configuration
- **Connection Pooling**: Configurable connection limits
- **Connection Lifetime**: Configurable connection timeouts
- **Environment Variables**: Database credentials via environment
- **SSL Support**: Configurable SSL connections

## Middleware & Security

### Built-in Middleware
1. **Logger**: Request/response logging
2. **Recover**: Panic recovery and error handling
3. **CORS**: Cross-origin resource sharing
4. **Security Headers**: Security-related HTTP headers

### CORS Configuration
```go
middleware.CORSWithConfig(middleware.CORSConfig{
    AllowOrigins: s.Config.AllowedOrigins,
    AllowMethods: []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
    AllowHeaders: []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
})
```

### Security Considerations
- Input validation and sanitization
- CORS policy configuration
- Request size limits
- Rate limiting (future enhancement)
- Authentication/Authorization (future enhancement)

## Error Handling

### Error Types
1. **Validation Errors**: Input validation failures
2. **Business Logic Errors**: Business rule violations
3. **Infrastructure Errors**: Database, network, or external service failures
4. **System Errors**: Unexpected application errors

### Error Response Format
```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {
            "field": "name",
            "issue": "Field is required"
        }
    }
}
```

### Error Handling Strategy
- Centralized error handling
- Consistent error response format
- Proper HTTP status codes
- Error logging and monitoring
