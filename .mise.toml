[hooks.enter]
script = ". scripts/hooks/enter.sh"
shell = "zsh"

[tools]
pre-commit = "4.3.0"
"go" = "1.24"
pipx = "latest"
"aqua:air-verse/air" = "latest"
"aqua:go-task/task" = "latest"
"aqua:swaggo/swag" = "latest"
"aqua:Zxilly/go-size-analyzer" = "latest"
"go:github.com/ethereum/go-ethereum/cmd/abigen" = "latest"
"go:github.com/sonatype-nexus-community/nancy" = "1"
"go:golang.org/x/tools/cmd/goimports" = "latest"
"golangci-lint" = "2"
"pipx:detect-secrets" = "1.5.0"
"ubi:grishy/gopkgview" = "latest"
"ubi:incu6us/goimports-reviser" = "latest"
"ubi:mrtazz/checkmake" = "latest"
"ubi:mvdan/gofumpt" = "latest"
"ubi:securego/gosec" = "2"
"go:go.uber.org/mock/mockgen" = "0.6.0"
