version: "2"

run:
  timeout: 5m
  tests: false

output:
  formats:
    text:
      path: "stderr"
      print-linter-name: true
      print-issued-lines: true
      colors: true
  path-prefix: ""
  sort-order:
    - severity
    - linter
    - file
  show-stats: true

issues:
  max-issues-per-linter: 0
  max-same-issues: 0

linters:
  default: "standard"
  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    - errorlint
    - exhaustive
    - funlen
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - goheader
    - gomodguard
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - lll
    - makezero
    - misspell
    - nakedret
    - nestif
    - nilerr
    - noctx
    - nolintlint
    - prealloc
    - predeclared
    - revive
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - thelper
    - tparallel
    - unconvert
    - unparam
    - unused
    - wastedassign
    - whitespace

  settings:
    errcheck:
      check-type-assertions: true
      check-blank: true

    gocognit:
      min-complexity: 20

    gocyclo:
      min-complexity: 15

    godot:
      scope: declarations
      exclude:
        - "^fixme:"
        - "^todo:"

    lll:
      line-length: 140

    misspell:
      locale: US

    nolintlint:
      allow-unused: false
      require-explanation: true
      require-specific: true

    revive:
      rules:
        - name: blank-imports
        - name: context-as-argument
        - name: context-keys-type
        - name: dot-imports
        - name: empty-block
        - name: error-naming
        - name: error-return
        - name: error-strings
        - name: errorf
        - name: if-return
        - name: increment-decrement
        - name: indent-error-flow
        - name: package-comments
        - name: range
        - name: receiver-naming
        - name: redefines-builtin-id
        - name: superfluous-else
        - name: time-naming
        - name: unexported-return
        - name: unreachable-code
        - name: unused-parameter
        - name: var-declaration
        - name: var-naming
          arguments:
            - [
                "OP_CREATE",
                "OP_UPDATE",
                "OP_DELETE",
                "OP_STATUS_TOGGLE",
                "OP_READ",
                "RESOURCE_COUNTRY",
                "RESOURCE_CURRENCY",
                "RESOURCE_CURRENCY_PAIR",
                "RESOURCE_EXCHANGE_RATE",
                "RESOURCE_USER",
              ]
    staticcheck:
      checks: ["all"]
