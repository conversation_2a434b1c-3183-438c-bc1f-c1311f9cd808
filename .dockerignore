# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
.github/

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# Build artifacts
target/
tmp/
coverage.out
coverage.html
*.log

# Test files
*_test.go
testdata/

# Environment files
.env
.env.*
!.env.example

# Dependencies
vendor/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.sublime-project
*.sublime-workspace

# Build tools
Makefile
Jenkinsfile
scripts/
tools/

# Config files
.golangci.yml
.pre-commit-config.yaml
.secrets.baseline
air.toml

# Docker files (except this one)
Dockerfile.*
docker-compose*.yml

# Temporary files
*.tmp
*.temp
