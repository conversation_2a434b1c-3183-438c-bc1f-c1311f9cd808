-- +goose Up
-- +goose StatementBegin
CREATE TABLE countries (
    id SERIAL PRIMARY KEY,
    external_id UUID NOT NULL UNIQUE,
    code VARCHAR(3) NOT NULL UNIQUE,
    name VA<PERSON>HAR(100) NOT NULL,
    flag_emoji VARCHAR(10),
    is_active BOOLEAN NOT NULL DEFAULT true,
    supports_sending BOOLEAN NOT NULL DEFAULT false,
    supports_receiving BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER,
    version INTEGER NOT NULL DEFAULT 1
);

CREATE INDEX idx_countries_external_id ON countries(external_id);
CREATE INDEX idx_countries_code ON countries(code);
CREATE INDEX idx_countries_active ON countries(is_active);
CREATE INDEX idx_countries_sending ON countries(supports_sending);
CREATE INDEX idx_countries_receiving ON countries(supports_receiving);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS countries CASCADE;
-- +goose StatementEnd
