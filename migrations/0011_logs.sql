-- +goose Up
CREATE TABLE IF NOT EXISTS logs (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    status VARCHAR(50), -- Log status
    request TEXT, -- Request data
    response TEXT, -- Response data
    error TEXT, -- Error message
    event TEXT, -- Event type
    description TEXT, -- Log description
    transaction_id INTEGER REFERENCES transactions(id) ON DELETE CASCADE, -- Reference to transaction internal ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_logs_external_id ON logs(external_id);
CREATE INDEX IF NOT EXISTS idx_logs_transaction_id ON logs(transaction_id);
CREATE INDEX IF NOT EXISTS idx_logs_status ON logs(status);
CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs(created_at);
CREATE INDEX IF NOT EXISTS idx_logs_deleted_at ON logs(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_logs_deleted_at;
DROP INDEX IF EXISTS idx_logs_created_at;
DROP INDEX IF EXISTS idx_logs_status;
DROP INDEX IF EXISTS idx_logs_transaction_id;
DROP INDEX IF EXISTS idx_logs_external_id;
DROP TABLE IF EXISTS logs;
