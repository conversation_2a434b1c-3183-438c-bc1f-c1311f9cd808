-- +goose Up
CREATE TABLE IF NOT EXISTS recipients (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    full_name VARCHAR(255) NOT NULL, -- Recipient full name
    bank_name VA<PERSON>HAR(255) NOT NULL, -- Bank name
    bank_account_number VARCHAR(255) NOT NULL, -- Bank account number
    bank_id INTEGER REFERENCES banks(id) ON DELETE CASCADE, -- Reference to bank internal ID
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- Reference to user internal ID
    destination_user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- Reference to destination user internal ID (nullable - recipient may not be a Telescope user yet)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_recipients_external_id ON recipients(external_id);
CREATE INDEX IF NOT EXISTS idx_recipients_user_id ON recipients(user_id);
CREATE INDEX IF NOT EXISTS idx_recipients_bank_account_number ON recipients(bank_account_number);
CREATE INDEX IF NOT EXISTS idx_recipients_bank_id ON recipients(bank_id);
CREATE INDEX IF NOT EXISTS idx_recipients_deleted_at ON recipients(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_recipients_deleted_at;
DROP INDEX IF EXISTS idx_recipients_bank_id;
DROP INDEX IF EXISTS idx_recipients_bank_account_number;
DROP INDEX IF EXISTS idx_recipients_user_id;
DROP INDEX IF EXISTS idx_recipients_external_id;
DROP TABLE IF EXISTS recipients;
