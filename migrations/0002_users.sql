-- +goose Up
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    first_name VARCHAR(255) NOT NULL, -- User first name
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL, -- User last name
    username <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL, -- User username
    email VARCHAR(255) UNIQUE NOT NULL, -- User email address
    password_hash VARCHAR(255) NOT NULL, -- Hashed password
    phone_number VARCHAR(20), -- User phone number
    status VARCHAR(25), -- User status (active, inactive, etc.)
    country_id INTEGER REFERENCES countries(id), -- Reference to country internal ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_users_external_id ON users(external_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_country_id ON users(country_id);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_users_deleted_at;
DROP INDEX IF EXISTS idx_users_country_id;
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_username;
DROP INDEX IF EXISTS idx_users_external_id;
DROP TABLE IF EXISTS users;
