-- +goose Up
-- Create providers table
CREATE TABLE IF NOT EXISTS providers (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    name VARCHAR(255) NOT NULL UNIQUE, -- Provider name (e.g., "<PERSON><PERSON><PERSON>", "<PERSON>")
    active BOOLEAN NOT NULL DEFAULT true, -- Whether provider is currently active/enabled
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- Create indexes for providers
CREATE INDEX IF NOT EXISTS idx_providers_external_id ON providers(external_id);
CREATE INDEX IF NOT EXISTS idx_providers_name ON providers(name);
CREATE INDEX IF NOT EXISTS idx_providers_active ON providers(active);
CREATE INDEX IF NOT EXISTS idx_providers_deleted_at ON providers(deleted_at);

-- +goose Down
-- Drop indexes for providers
DROP INDEX IF EXISTS idx_providers_deleted_at;
DROP INDEX IF EXISTS idx_providers_active;
DROP INDEX IF EXISTS idx_providers_name;
DROP INDEX IF EXISTS idx_providers_external_id;

-- Drop table
DROP TABLE IF EXISTS providers;
