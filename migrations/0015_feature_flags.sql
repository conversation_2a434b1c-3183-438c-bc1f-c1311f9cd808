-- +goose Up
CREATE TABLE IF NOT EXISTS feature_flags (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    name VARCHAR(255) UNIQUE NOT NULL, -- Feature flag name
    description TEXT, -- Feature flag description
    is_enabled BOOLEAN DEFAULT FALSE, -- Whether feature flag is enabled
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_feature_flags_external_id ON feature_flags(external_id);
CREATE INDEX IF NOT EXISTS idx_feature_flags_name ON feature_flags(name);
CREATE INDEX IF NOT EXISTS idx_feature_flags_is_enabled ON feature_flags(is_enabled);
CREATE INDEX IF NOT EXISTS idx_feature_flags_deleted_at ON feature_flags(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_feature_flags_deleted_at;
DROP INDEX IF EXISTS idx_feature_flags_is_enabled;
DROP INDEX IF EXISTS idx_feature_flags_name;
DROP INDEX IF EXISTS idx_feature_flags_external_id;
DROP TABLE IF EXISTS feature_flags;
