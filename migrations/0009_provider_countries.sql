-- +goose Up
-- Create provider_countries junction table
CREATE TABLE IF NOT EXISTS provider_countries (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses (generated by backend)
    provider_id INTEGER NOT NULL REFERENCES providers(id) ON DELETE CASCADE, -- Reference to provider internal ID
    country_id INTEGER NOT NULL REFERENCES countries(id) ON DELETE CASCADE, -- Reference to country internal ID
    active BOOLEAN NOT NULL DEFAULT true, -- Whether this provider-country relationship is active
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE, -- Soft delete timestamp (NULL if not deleted)
    UNIQUE(provider_id, country_id) -- Ensure unique provider-country combinations
);

-- Create indexes for provider_countries
CREATE INDEX IF NOT EXISTS idx_provider_countries_external_id ON provider_countries(external_id);
CREATE INDEX IF NOT EXISTS idx_provider_countries_provider_id ON provider_countries(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_countries_country_id ON provider_countries(country_id);
CREATE INDEX IF NOT EXISTS idx_provider_countries_active ON provider_countries(active);
CREATE INDEX IF NOT EXISTS idx_provider_countries_deleted_at ON provider_countries(deleted_at);

-- +goose Down
-- Drop indexes for provider_countries
DROP INDEX IF EXISTS idx_provider_countries_deleted_at;
DROP INDEX IF EXISTS idx_provider_countries_active;
DROP INDEX IF EXISTS idx_provider_countries_country_id;
DROP INDEX IF EXISTS idx_provider_countries_provider_id;
DROP INDEX IF EXISTS idx_provider_countries_external_id;

-- Drop table
DROP TABLE IF EXISTS provider_countries;
