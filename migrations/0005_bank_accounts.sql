-- +goose Up
CREATE TABLE IF NOT EXISTS bank_accounts (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    bank_name VARCHAR(255) NOT NULL, -- Bank name
    account_number VARCHAR(255) NOT NULL, -- Bank account number
    full_name VA<PERSON>HAR(255) NOT NULL, -- Account holder full name
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- Reference to user internal ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_bank_accounts_external_id ON bank_accounts(external_id);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_user_id ON bank_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_account_number ON bank_accounts(account_number);
CREATE INDEX IF NOT EXISTS idx_bank_accounts_deleted_at ON bank_accounts(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_bank_accounts_deleted_at;
DROP INDEX IF EXISTS idx_bank_accounts_account_number;
DROP INDEX IF EXISTS idx_bank_accounts_user_id;
DROP INDEX IF EXISTS idx_bank_accounts_external_id;
DROP TABLE IF EXISTS bank_accounts;
