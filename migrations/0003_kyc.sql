-- +goose Up
CREATE TABLE IF NOT EXISTS kyc (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    identity_type VARCHAR(50) NOT NULL, -- Type of identity document
    identity_number VA<PERSON>HAR(255) NOT NULL, -- Identity document number
    address TEXT, -- User address
    country VARCHAR(255), -- Country name
    state VARCHAR(255), -- State/province
    zip_number VARCHAR(20), -- Postal/ZIP code
    provider VARCHAR(255), -- KYC provider (sumsub, advance.ai, kredibel, etc)
    status VARCHAR(32), -- KYC status (rejected, approved, pending, etc)
    reason TEXT DEFAULT NULL, -- Reason for rejection
    user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE, -- Reference to user internal ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_kyc_external_id ON kyc(external_id);
CREATE INDEX IF NOT EXISTS idx_kyc_user_id ON kyc(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_identity_number ON kyc(identity_number);
CREATE INDEX IF NOT EXISTS idx_kyc_deleted_at ON kyc(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_kyc_deleted_at;
DROP INDEX IF EXISTS idx_kyc_identity_number;
DROP INDEX IF EXISTS idx_kyc_user_id;
DROP INDEX IF EXISTS idx_kyc_external_id;
DROP TABLE IF EXISTS kyc;
