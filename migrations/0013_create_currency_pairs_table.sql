-- +goose Up
-- +goose StatementBegin
CREATE TABLE currency_pairs (
    id SERIAL PRIMARY KEY,
    external_id UUID NOT NULL UNIQUE,
    from_currency_id INTEGER NOT NULL REFERENCES currencies(id),
    to_currency_id INTEGER NOT NULL REFERENCES currencies(id),
    is_active BOOLEAN NOT NULL DEFAULT true,
    min_amount DECIMAL(20,8) NOT NULL DEFAULT 0,
    max_amount DECIMAL(20,8),
    delivery_time_min INTEGER NOT NULL,
    delivery_time_max INTEGER NOT NULL,
    delivery_description VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER,
    version INTEGER NOT NULL DEFAULT 1,
    UNIQUE(from_currency_id, to_currency_id)
);

CREATE INDEX idx_currency_pairs_external_id ON currency_pairs(external_id);
CREATE INDEX idx_currency_pairs_from_currency ON currency_pairs(from_currency_id);
CREATE INDEX idx_currency_pairs_to_currency ON currency_pairs(to_currency_id);
CREATE INDEX idx_currency_pairs_active ON currency_pairs(is_active);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS currency_pairs CASCADE;
-- +goose StatementEnd
