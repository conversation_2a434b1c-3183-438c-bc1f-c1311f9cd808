-- +goose Up
CREATE TABLE IF NOT EXISTS banks (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    name VARCHAR(255) NOT NULL, -- Bank name
    country_id INTEGER NOT NULL, -- Reference to country internal ID
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_banks_external_id ON banks(external_id);
CREATE INDEX IF NOT EXISTS idx_banks_country_id ON banks(country_id);
CREATE INDEX IF NOT EXISTS idx_banks_deleted_at ON banks(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_banks_deleted_at;
DROP INDEX IF EXISTS idx_banks_country_id;
DROP INDEX IF EXISTS idx_banks_external_id;
DROP TABLE IF EXISTS banks;
