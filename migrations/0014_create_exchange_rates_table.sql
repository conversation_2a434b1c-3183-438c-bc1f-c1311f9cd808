-- +goose Up
-- +goose StatementBegin
CREATE TABLE exchange_rates (
    id SERIAL PRIMARY KEY,
    external_id UUID NOT NULL UNIQUE,
    currency_pair_id INTEGER NOT NULL REFERENCES currency_pairs(id),
    rate DECIMAL(20,8) NOT NULL,
    source VARCHAR(50) NOT NULL,
    source_metadata JSONB,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    version INTEGER NOT NULL DEFAULT 1,
    UNIQUE(currency_pair_id, valid_from)
);

CREATE INDEX idx_exchange_rates_external_id ON exchange_rates(external_id);
CREATE INDEX idx_exchange_rates_currency_pair ON exchange_rates(currency_pair_id);
CREATE INDEX idx_exchange_rates_valid_period ON exchange_rates(valid_from, valid_until);
CREATE INDEX idx_exchange_rates_active ON exchange_rates(is_active);
CREATE INDEX idx_exchange_rates_source ON exchange_rates(source);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS exchange_rates CASCADE;
-- +goose StatementEnd
