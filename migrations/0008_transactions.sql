-- +goose Up
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses and client interactions (generated by backend)
    total_amount DECIMAL(15,2) NOT NULL, -- Transaction total amount
    currency VARCHAR(10) NOT NULL DEFAULT 'USD', -- Transaction currency
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- Reference to user internal ID
    bank_id INTEGER REFERENCES banks(id), -- Reference to bank internal ID
    recipient_id INTEGER REFERENCES recipients(id), -- Reference to recipient internal ID
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- Transaction status
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete timestamp (NULL if not deleted)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_transactions_external_id ON transactions(external_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_bank_id ON transactions(bank_id);
CREATE INDEX IF NOT EXISTS idx_transactions_recipient_id ON transactions(recipient_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_deleted_at ON transactions(deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_transactions_deleted_at;
DROP INDEX IF EXISTS idx_transactions_created_at;
DROP INDEX IF EXISTS idx_transactions_status;
DROP INDEX IF EXISTS idx_transactions_recipient_id;
DROP INDEX IF EXISTS idx_transactions_bank_id;
DROP INDEX IF EXISTS idx_transactions_user_id;
DROP INDEX IF EXISTS idx_transactions_external_id;
DROP TABLE IF EXISTS transactions;
