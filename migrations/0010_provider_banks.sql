-- +goose Up
-- Create provider_banks junction table
CREATE TABLE IF NOT EXISTS provider_banks (
    id SERIAL PRIMARY KEY, -- Internal primary key for database operations
    external_id UUID NOT NULL UNIQUE, -- External UUID v7 for API responses (generated by backend)
    provider_id INTEGER NOT NULL REFERENCES providers(id) ON DELETE CASCADE, -- Reference to provider internal ID
    bank_id INTEGER NOT NULL REFERENCES banks(id) ON DELETE CASCADE, -- Reference to bank internal ID
    internal_code VARCHAR(50), -- Telescope's internal code for this bank-provider relationship
    external_code VARCHAR(50), -- Provider's code/ID for this bank
    active BOOLEAN NOT NULL DEFAULT true, -- Whether this provider-bank relationship is active
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record creation timestamp
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Record last update timestamp
    deleted_at TIMESTAMP WITH TIME ZONE, -- Soft delete timestamp (NULL if not deleted)
    UNIQUE(provider_id, bank_id) -- Ensure unique provider-bank combinations
);

-- Create indexes for provider_banks
CREATE INDEX IF NOT EXISTS idx_provider_banks_external_id ON provider_banks(external_id);
CREATE INDEX IF NOT EXISTS idx_provider_banks_provider_id ON provider_banks(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_banks_bank_id ON provider_banks(bank_id);
CREATE INDEX IF NOT EXISTS idx_provider_banks_internal_code ON provider_banks(internal_code);
CREATE INDEX IF NOT EXISTS idx_provider_banks_external_code ON provider_banks(external_code);
CREATE INDEX IF NOT EXISTS idx_provider_banks_active ON provider_banks(active);
CREATE INDEX IF NOT EXISTS idx_provider_banks_deleted_at ON provider_banks(deleted_at);

-- +goose Down
-- Drop indexes for provider_banks
DROP INDEX IF EXISTS idx_provider_banks_deleted_at;
DROP INDEX IF EXISTS idx_provider_banks_active;
DROP INDEX IF EXISTS idx_provider_banks_external_code;
DROP INDEX IF EXISTS idx_provider_banks_internal_code;
DROP INDEX IF EXISTS idx_provider_banks_bank_id;
DROP INDEX IF EXISTS idx_provider_banks_provider_id;
DROP INDEX IF EXISTS idx_provider_banks_external_id;

-- Drop table
DROP TABLE IF EXISTS provider_banks;
