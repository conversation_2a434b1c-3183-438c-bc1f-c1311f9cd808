-- +goose Up
-- +goose StatementBegin
CREATE TABLE currencies (
    id SERIAL PRIMARY KEY,
    external_id UUID NOT NULL UNIQUE,
    code VA<PERSON>HAR(3) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    decimal_places INTEGER NOT NULL DEFAULT 2,
    country_id INTEGER REFERENCES countries(id),
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER,
    version INTEGER NOT NULL DEFAULT 1
);

CREATE INDEX idx_currencies_external_id ON currencies(external_id);
CREATE INDEX idx_currencies_code ON currencies(code);
CREATE INDEX idx_currencies_country ON currencies(country_id);
CREATE INDEX idx_currencies_active ON currencies(is_active);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS currencies CASCADE;
-- +goose StatementEnd
