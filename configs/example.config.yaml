server:
  host: "localhost"
  port: "8080"
  mode: "debug"
  allowed_origins:
    - "*"

database:
  driver: "postgres"
  host: "localhost"
  port: "5432"
  user: "postgres"
  password: "password"
  database: "telescope"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

logger:
  level: "info"
  format: "json"

provider:
  idrx:
    base_url: "https://api.example.com"
    timeout: "30s"
