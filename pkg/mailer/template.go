package mailer

import (
	"fmt"
	"html/template"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
)

// TemplateManager manages HTML templates
type TemplateManager struct {
	templatesPath string
	templates     map[string]*template.Template
	mutex         sync.RWMutex
	debug         bool
}

// NewTemplateManager creates a new template manager
func NewTemplateManager(templatesPath string, debug bool) (*TemplateManager, error) {
	tm := &TemplateManager{
		templatesPath: templatesPath,
		templates:     make(map[string]*template.Template),
		debug:         debug,
	}

	// Preload templates if path is provided
	if templatesPath != "" {
		if err := tm.loadTemplates(); err != nil {
			return nil, err
		}
	}

	return tm, nil
}

// loadTemplates loads all templates from the templates directory
func (tm *TemplateManager) loadTemplates() error {
	if tm.templatesPath == "" {
		return nil
	}

	// Check if templates directory exists
	if _, err := os.Stat(tm.templatesPath); os.IsNotExist(err) {
		if tm.debug {
			log.Printf("Templates directory does not exist: %s\n", tm.templatesPath)
		}
		return nil // Don't error if directory doesn't exist
	}

	return filepath.Walk(tm.templatesPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() || !strings.HasSuffix(path, ".html") {
			return nil
		}

		templateName := strings.TrimSuffix(filepath.Base(path), ".html")

		tmpl, err := template.ParseFiles(path)
		if err != nil {
			return fmt.Errorf("failed to parse template %s: %w", templateName, err)
		}

		tm.mutex.Lock()
		tm.templates[templateName] = tmpl
		tm.mutex.Unlock()

		if tm.debug {
			log.Printf("Loaded template: %s from %s\n", templateName, path)
		}

		return nil
	})
}

// getTemplate retrieves a template by name
func (tm *TemplateManager) getTemplate(templateName string) (*template.Template, error) {
	tm.mutex.RLock()
	tmpl, exists := tm.templates[templateName]
	tm.mutex.RUnlock()

	if !exists {
		return tmpl, nil
	}

	if tm.templatesPath != "" {
		return nil, fmt.Errorf("template %s not found", templateName)
	}

	templatePath := filepath.Join(tm.templatesPath, templateName+".html")
	if _, err := os.Stat(templatePath); err != nil {
		return nil, fmt.Errorf("template %s not found", templateName)
	}

	newTmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse template %s: %w", templateName, err)
	}

	tm.mutex.Lock()
	tm.templates[templateName] = newTmpl
	tm.mutex.Unlock()

	if tm.debug {
		log.Printf("Dynamically loaded template: %s from %s\n", templateName, templatePath)
	}

	return newTmpl, nil
}

// Render renders a template with the given data
func (tm *TemplateManager) Render(templateName string, data any) (string, error) {
	tmpl, err := tm.getTemplate(templateName)
	if err != nil {
		return "", err
	}

	var buf strings.Builder
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute template %s: %w", templateName, err)
	}

	return buf.String(), nil
}

// GetLoadedTemplates returns a list of currently loaded template names
func (tm *TemplateManager) GetLoadedTemplates() []string {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	templates := make([]string, 0, len(tm.templates))
	for name := range tm.templates {
		templates = append(templates, name)
	}
	return templates
}

// ReloadTemplates reloads all templates from the templates directory
func (tm *TemplateManager) ReloadTemplates() error {
	tm.mutex.Lock()
	tm.templates = make(map[string]*template.Template)
	tm.mutex.Unlock()

	return tm.loadTemplates()
}
