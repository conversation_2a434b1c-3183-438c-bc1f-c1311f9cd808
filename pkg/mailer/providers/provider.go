// Package providers provides mailer with different providers
package providers

import (
	"context"
	"fmt"
)

// Provider defines the interface for email providers
type Provider interface {
	Send(ctx context.Context, to, subject, htmlContent string) error
}

// ProviderType represents the email service provider type
type ProviderType string

const (
	Resend   ProviderType = "resend"
	SendGrid ProviderType = "sendgrid"
)

// NewProvider creates a new provider based on the type
func NewProvider(providerType ProviderType, apiKey, fromEmail, fromName string, debug bool) (Provider, error) {
	switch providerType {
	case Resend:
		return NewResendProvider(apiKey, fromEmail, fromName, debug), nil
	case SendGrid:
		return NewSendGridProvider(apiKey, fromEmail, fromName, debug), nil
	default:
		return nil, fmt.Errorf("unsupported provider: %s", providerType)
	}
}
