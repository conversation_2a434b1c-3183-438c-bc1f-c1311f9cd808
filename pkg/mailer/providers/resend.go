package providers

import (
	"context"
	"fmt"
	"log"

	"github.com/resend/resend-go/v2"
)

// ResendProvider implements the Provider interface for Resend
type ResendProvider struct {
	client    *resend.Client
	fromEmail string
	fromName  string
	debug     bool
}

// NewResendProvider creates a new Resend provider
func NewResendProvider(apiKey, fromEmail, fromName string, debug bool) *ResendProvider {
	return &ResendProvider{
		client:    resend.NewClient(apiKey),
		fromEmail: fromEmail,
		fromName:  fromName,
		debug:     debug,
	}
}

// Send sends an email using Resend
func (r *ResendProvider) Send(ctx context.Context, to, subject, htmlContent string) error {
	if r.debug {
		log.Printf("Sending email via Resend to: %s, subject: %s\n", to, subject)
	}

	params := &resend.SendEmailRequest{
		From:    fmt.Sprintf("%s <%s>", r.fromName, r.fromEmail),
		To:      []string{to},
		Subject: subject,
		Html:    htmlContent,
	}

	sent, err := r.client.Emails.SendWithContext(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to send email via Resend: %w", err)
	}

	if r.debug {
		log.Printf("Email sent successfully via Resend to: %s, ID: %s\n", to, sent.Id)
	}

	return nil
}
