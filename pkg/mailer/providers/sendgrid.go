package providers

import (
	"context"
	"fmt"
	"log"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

// SendGridProvider implements the Provider interface for SendGrid
type SendGridProvider struct {
	client    *sendgrid.Client
	fromEmail string
	fromName  string
	debug     bool
}

// NewSendGridProvider creates a new SendGrid provider
func NewSendGridProvider(apiKey, fromEmail, fromName string, debug bool) *SendGridProvider {
	return &SendGridProvider{
		client:    sendgrid.NewSendClient(apiKey),
		fromEmail: fromEmail,
		fromName:  fromName,
		debug:     debug,
	}
}

// Send sends an email using SendGrid
func (s *SendGridProvider) Send(ctx context.Context, to, subject, htmlContent string) error {
	if s.debug {
		log.Printf("Sending email via SendGrid to: %s, subject: %s\n", to, subject)
	}

	from := mail.NewEmail(s.fromName, s.fromEmail)
	toEmail := mail.NewEmail("", to)

	message := mail.NewSingleEmail(from, subject, toEmail, "", htmlContent)

	response, err := s.client.SendWithContext(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send email via SendGrid: %w", err)
	}

	if response.StatusCode >= 400 {
		return fmt.Errorf("SendGrid API error: status %d, body: %s", response.StatusCode, response.Body)
	}

	if s.debug {
		log.Printf("Email sent successfully via SendGrid to: %s (status: %d)\n", to, response.StatusCode)
	}

	return nil
}
