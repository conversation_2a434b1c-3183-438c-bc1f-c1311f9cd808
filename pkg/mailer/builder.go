package mailer

// Builder provides a fluent interface for creating mailer configurations
type Builder struct {
	config Config
}

// NewBuilder creates a new mailer builder
func NewBuilder() *Builder {
	return &Builder{
		config: Config{
			FromName: "No Reply",
			Debug:    false,
		},
	}
}

// WithProvider sets the email provider
func (b *Builder) WithProvider(provider Provider) *Builder {
	b.config.Provider = provider
	return b
}

// WithResend sets the provider to Resend
func (b *Builder) WithResend() *Builder {
	b.config.Provider = ProviderResend
	return b
}

// WithSendGrid sets the provider to SendGrid
func (b *Builder) WithSendGrid() *Builder {
	b.config.Provider = ProviderSendGrid
	return b
}

// WithAPIKey sets the API key
func (b *Builder) WithAPIKey(apiKey string) *Builder {
	b.config.APIKey = apiKey // pragma: allowlist secret
	return b
}

// WithFromEmail sets the from email address
func (b *Builder) WithFromEmail(email string) *Builder {
	b.config.FromEmail = email
	return b
}

// WithFromName sets the from name
func (b *Builder) WithFromName(name string) *Builder {
	b.config.FromName = name
	return b
}

// WithTemplatesPath sets the templates directory path
func (b *Builder) WithTemplatesPath(path string) *Builder {
	b.config.TemplatesPath = path
	return b
}

// WithDebug enables or disables debug mode
func (b *Builder) WithDebug(debug bool) *Builder {
	b.config.Debug = debug
	return b
}

// Build creates the mailer instance
func (b *Builder) Build() (Mailer, error) {
	return New(b.config)
}

// Convenience functions for quick setup

// NewResendMailer creates a new Resend mailer with minimal configuration
func NewResendMailer(apiKey, fromEmail string) (Mailer, error) {
	return NewBuilder().
		WithResend().
		WithAPIKey(apiKey).
		WithFromEmail(fromEmail).
		Build()
}

// NewSendGridMailer creates a new SendGrid mailer with minimal configuration
func NewSendGridMailer(apiKey, fromEmail string) (Mailer, error) {
	return NewBuilder().
		WithSendGrid().
		WithAPIKey(apiKey).
		WithFromEmail(fromEmail).
		Build()
}

// NewResendMailerWithTemplates creates a Resend mailer with templates support
func NewResendMailerWithTemplates(apiKey, fromEmail, templatesPath string) (Mailer, error) {
	return NewBuilder().
		WithResend().
		WithAPIKey(apiKey).
		WithFromEmail(fromEmail).
		WithTemplatesPath(templatesPath).
		Build()
}

// NewSendGridMailerWithTemplates creates a SendGrid mailer with templates support
func NewSendGridMailerWithTemplates(apiKey, fromEmail, templatesPath string) (Mailer, error) {
	return NewBuilder().
		WithSendGrid().
		WithAPIKey(apiKey).
		WithFromEmail(fromEmail).
		WithTemplatesPath(templatesPath).
		Build()
}
