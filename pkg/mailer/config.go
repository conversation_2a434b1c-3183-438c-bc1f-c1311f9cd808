package mailer

import "fmt"

// Config holds the configuration for the mailer
type Config struct {
	Provider      Provider
	APIKey        string
	FromEmail     string
	FromName      string
	TemplatesPath string
	Debug         bool
}

// Provider represents the email service provider
type Provider string

const (
	ProviderResend   Provider = "resend"
	ProviderSendGrid Provider = "sendgrid"
)

// ValidateConfig validates the mailer configuration
func ValidateConfig(config Config) error {
	if config.APIKey == "" {
		return fmt.Errorf("API key is required")
	}
	if config.FromEmail == "" {
		return fmt.Errorf("from email is required")
	}
	if config.Provider == "" {
		return fmt.Errorf("provider is required")
	}
	if config.Provider != ProviderResend && config.Provider != ProviderSendGrid {
		return fmt.Errorf("unsupported provider: %s", config.Provider)
	}
	if config.FromName == "" {
		config.FromName = "No Reply" // Set default if not provided
	}
	return nil
}
