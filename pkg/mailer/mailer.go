// Package mailer provides functionality for sending emails using different providers and templating.
package mailer

import (
	"context"
	"fmt"

	"telescope-be/pkg/mailer/providers"
)

// Mailer defines the interface for sending templated emails
type Mailer interface {
	SendTemplate(ctx context.Context, to, subject, template string, data any) error
}

// mailer implements the Mailer interface
type mailer struct {
	config      Config
	templateMgr *TemplateManager
	provider    providers.Provider
}

// New creates a new mailer instance with the given configuration
func New(config Config) (Mailer, error) {
	if err := ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	// Initialize template manager
	templateMgr, err := NewTemplateManager(config.TemplatesPath, config.Debug)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize template manager: %w", err)
	}

	// Initialize provider
	provider, err := providers.NewProvider(
		providers.ProviderType(config.Provider),
		config.<PERSON>Key,
		config.FromEmail,
		config.FromName,
		config.Debug,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize provider: %w", err)
	}

	return &mailer{
		config:      config,
		templateMgr: templateMgr,
		provider:    provider,
	}, nil
}

// SendTemplate sends an email using the specified template
func (m *mailer) SendTemplate(ctx context.Context, to, subject, templateName string, data any) error {
	// Render the template
	htmlContent, err := m.templateMgr.Render(templateName, data)
	if err != nil {
		return fmt.Errorf("failed to render template: %w", err)
	}

	// Send email using the provider
	return m.provider.Send(ctx, to, subject, htmlContent)
}
