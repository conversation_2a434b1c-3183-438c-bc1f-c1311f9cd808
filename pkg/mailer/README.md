# Mailer Package

A composable email delivery layer that wraps third-party providers (Resend and SendGrid) with reusable templates and a small builder for simple configuration. The package is designed to be plugged into services that already manage context lifecycles and secrets, and it keeps provider-specific concerns isolated from the rest of the codebase.

## Features
- Templated HTML emails backed by Go's `html/template` with optional preloading.
- Provider abstraction with built-in Resend and SendGrid implementations and support for custom providers.
- Fluent builder for ergonomic configuration in application bootstrap code.
- Debug mode that logs template loading and outbound requests without changing business logic.
- Concurrency-safe template cache with on-demand reloading helpers for long-running processes.

## Installation
The package lives inside the `telescope-be` module. Import it directly:

```go
import "telescope-be/pkg/mailer"
```

Make sure your application already depends on the top-level module (run `go mod tidy` after adding the import). Provider-specific SDKs (`github.com/resend/resend-go/v2` and `github.com/sendgrid/sendgrid-go`) are pulled in automatically via `go.mod`.

## Quick Start
Create a mailer with the fluent builder and send a templated email:

```go
package main

import (
    "context"

    "telescope-be/pkg/mailer"
)

func main() {
    emailer, err := mailer.NewBuilder().
        WithResend().
        WithAPIKey(os.Getenv("RESEND_API_KEY")).
        WithFromEmail("<EMAIL>").
        WithFromName("Lumine Alerts").
        WithTemplatesPath("./templates/email").
        WithDebug(true).
        Build()
    if err != nil {
        log.Fatal(err)
    }

    data := struct {
        Username string
        Link     string
    }{
        Username: "Alice",
        Link:     "https://example.com/reset",
    }

    if err := emailer.SendTemplate(context.Background(), "<EMAIL>", "Reset your password", "reset-password", data); err != nil {
        log.Fatal(err)
    }
}
```

The snippet above assumes a file named `reset-password.html` exists inside the `./templates/email` directory.

## Configuration Reference
The `mailer.Config` struct contains all runtime options:

| Field | Type | Required | Description |
| --- | --- | --- | --- |
| `Provider` | `mailer.Provider` | ✔︎ | Provider identifier (`mailer.ProviderResend` or `mailer.ProviderSendGrid`). |
| `APIKey` | `string` | ✔︎ | Secret token for the chosen provider. |
| `FromEmail` | `string` | ✔︎ | Sender address. |
| `FromName` | `string` | optional | Display name; defaults to `"No Reply"`. |
| `TemplatesPath` | `string` | optional | Directory where `.html` templates live. Leave empty to manage templates manually. |
| `Debug` | `bool` | optional | When true, logs template loads and outbound requests. |

You can build the configuration manually:

```go
cfg := mailer.Config{
    Provider:      mailer.ProviderSendGrid,
    APIKey:        os.Getenv("SENDGRID_API_KEY"),
    FromEmail:     "<EMAIL>",
    FromName:      "Lumine Alerts",
    TemplatesPath: "./templates/email",
    Debug:         true,
}

emailer, err := mailer.New(cfg)
```

`mailer.ValidateConfig` is called automatically by `mailer.New` and the builder, so missing or unsupported values are reported early with descriptive errors.

## Template Management
- Templates are standard Go HTML templates stored as separate `.html` files. The filename (without extension) becomes the template key (`reset-password.html` → `reset-password`).
- When `TemplatesPath` is configured, templates are preloaded at start-up. Missing directories are ignored unless `Debug` is true, in which case a log line is emitted.
- Templates are cached in memory and protected with a `sync.RWMutex`. Use `TemplateManager.ReloadTemplates()` (exposed through the `Mailer` via type assertion) if you want to refresh templates without restarting the process.
- Rendering failures (syntax errors or missing fields in the data) return an error that bubbles up from `SendTemplate`.

If you prefer to manage templates outside this package, leave `TemplatesPath` empty and populate the cache yourself by calling `TemplateManager.Render` with pre-parsed templates.

## Provider Abstraction
Providers live under `pkg/mailer/providers` and implement the simple interface:

```go
type Provider interface {
    Send(ctx context.Context, to, subject, htmlContent string) error
}
```

The factory `providers.NewProvider` is used internally by `mailer.New`. To add a new provider:

1. Implement the interface in a new file under `pkg/mailer/providers`.
2. Extend the `ProviderType` enum and switch in `providers.NewProvider`.
3. Expose a convenience helper in `builder.go` if desired.

The existing providers wrap the official SDKs and surface detailed errors (including HTTP status and response body for SendGrid).

## Debugging & Observability
- Set `Debug` to `true` during development to log template loads and outbound requests.
- Providers propagate context deadlines and cancellations. Pass a context with timeout when you need strict delivery SLAs.
- Errors from provider SDKs are wrapped with context (e.g., `failed to send email via SendGrid`). Log them upstream for easier troubleshooting.

## Testing Tips
- Provide a fake implementation of the `Mailer` or `providers.Provider` interface in tests to avoid hitting external APIs.
- For integration tests, point `TemplatesPath` to a temporary directory populated with test fixtures.
- When running in CI, skip template preloading by leaving `TemplatesPath` empty and mock the template manager if you only need to assert provider calls.

## Common Pitfalls
- **"API key is required"** – ensure the relevant environment variable is loaded before constructing the mailer.
- **"unsupported provider"** – use the typed constants (`mailer.ProviderResend`/`mailer.ProviderSendGrid`) or the helper methods.
- **Template not found** – verify the template filename matches the key you pass to `SendTemplate` and that the directory is mounted in your runtime environment.

With these pieces in place you can centralize email delivery logic, switch providers easily, and keep template rendering consistent across your services.
