# IDRX Go SDK

An SDK for IDRX stablecoin operations. Handles dual authentication, multi-chain transactions, and KYC workflows.

## Features

- **Dual auth system**: Business + user key management
- **Complete endpoint coverage**: Account, transaction, bridge operations
- **Multi-chain support**: Polygon (137), BSC (56), Ethereum (1), Arbitrum (42161)
- **Production hardening**: Context cancellation, structured errors, retry logic
- **Type safety**: Full validation on requests/responses

## Authentication Context

**Business Auth:** Organization management, user onboarding
**User Auth:** Individual transactions, bank accounts

```go
// Business operations
bizClient := idrx.NewClient(idrx.WithBusinessAuth(bizKey, bizSecret))

// User operations (keys from onboarding response)
userClient := idrx.NewClient(idrx.WithUserAuth(userKey, userSecret))
```

## Core Operations

### User Onboarding (Business Auth Required)

```go
import "os"

idFile, _ := os.Open("id-document.jpg")
defer idFile.Close()

resp, err := bizClient.Account.Onboard(ctx, &models.OnboardingRequest{
    Email:    "<EMAIL>",
    Fullname: "<PERSON> Doe",
    Address:  "123 Main St",
    IDNumber: "ID123456789",
    IDFile:   idFile,
})

// Store user credentials for transaction operations
userKey := resp.APIKey
userSecret := resp.APISecret
```

### IDRX Minting (User Auth Required)

```go
resp, err := userClient.Transaction.MintRequest(ctx, &models.MintRequest{
    ToBeMinted:               "100000", // 100K IDRX
    DestinationWalletAddress: "0x742d35Cc...",
    NetworkChainID:           "137", // Polygon
    ExpiryPeriod:             3600,
})
// Use resp.PaymentURL for user payment flow
```

### IDRX Redemption (User Auth Required)

```go
resp, err := userClient.Transaction.RedeemRequest(ctx, &models.RedeemRequest{
    TxHash:          "0x1234...",
    NetworkChainID:  "137",
    AmountTransfer:  "50000",
    BankAccount:     "**********",
    BankCode:        "BCA",
    BankName:        "Bank Central Asia",
    BankAccountName: "John Doe",
    WalletAddress:   "0x742d35Cc...",
    Notes:           "Withdrawal",
})
// Track via resp.TransactionID
```

### Bank Account Management (User Auth Required)

```go
// Add account
account, err := userClient.Account.AddBankAccount(ctx, &models.AddBankAccountRequest{
    BankAccountNumber: "**********",
    BankCode:          "BCA",
})

// List accounts
accounts, err := userClient.Account.GetBankAccounts(ctx)

// Remove account
err = userClient.Account.DeleteBankAccount(ctx, account.ID)
```

## Advanced Usage

### Custom HTTP Client

```go
httpClient := &http.Client{
    Timeout: time.Second * 60,
    Transport: &http.Transport{
        MaxIdleConns: 20,
    },
}

client := idrx.NewClient(
    idrx.WithHTTPClient(httpClient),
    idrx.WithBusinessAuth(apiKey, secretKey),
    idrx.WithTimeout(time.Second * 45),
)
```

### Error Handling

```go
mintResp, err := client.Transaction.MintRequest(ctx, mintReq)
if err != nil {
    // Check if it's an API error
    if apiErr, ok := err.(*models.APIError); ok {
        log.Printf("API Error [%d]: %s", apiErr.StatusCode, apiErr.Message)
        if apiErr.Details != nil {
            log.Printf("Details: %+v", apiErr.Details)
        }
    } else {
        log.Printf("Other error: %v", err)
    }
    return
}
```

### Context with Timeout

```go
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

response, err := client.Account.GetMembers(ctx)
if err != nil {
    log.Fatal(err)
}
```

## Supported Networks

- **Polygon (137)**: Primary network for IDRX operations
- **BNB Smart Chain (56)**: Cross-chain operations
- **Ethereum (1)**: Mainnet operations
- **Arbitrum (42161)**: Layer 2 operations

## Transaction Limits

### IDRX Minting

- **Minimum**: 20,000 IDR
- **Maximum**: 1,000,000,000 IDR

### Other Stablecoins (USDT/USDC)

- **Minimum**: $2 USD
- **Maximum**: $5,555 USD

### Bank Transfer Fees

- **≤ Rp250M**: Rp5,000 (real-time processing)
- **> Rp250M**: Rp35,000 (office hours processing)

## Error Handling

The SDK provides structured error handling with `APIError` type:

```go
type APIError struct {
    StatusCode int         `json:"statusCode"`
    Code       string      `json:"code,omitempty"`
    Message    string      `json:"message"`
    Details    interface{} `json:"details,omitempty"`
}
```

Common status codes:

- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication issues)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `429`: Rate Limited
- `500`: Internal Server Error

## License

This SDK is part of the Telescope Backend project and follows the same licensing terms.

## Support

For API documentation, visit:

- [IDRX Documentation](https://docs.idrx.co)
