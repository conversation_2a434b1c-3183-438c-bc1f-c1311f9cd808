// Package blockchain provides network configuration and constants for IDRX token operations across multiple blockchains.
package blockchain

import (
	"time"

	"github.com/ethereum/go-ethereum/common"
)

// NetworkConfig represents configuration for a blockchain network
type NetworkConfig struct {
	ChainID         uint64
	Name            string
	RPCEndpoints    []string // Multiple RPC endpoints for failover
	ContractAddress common.Address
	BlockTime       time.Duration
	GasLimit        uint64
	MaxGasPrice     uint64 // in wei
	IsTestnet       bool
}

// Network identifier constants for better type safety
const (
	BaseMainnet     = "BaseMainnet"
	PolygonMainnet  = "PolygonMainnet"
	BSCMainnet      = "BSCMainnet"
	LiskMainnet     = "LiskMainnet"
	EthereumMainnet = "EthereumMainnet"
)

// SupportedNetworks contains all IDRX supported blockchain networks
var SupportedNetworks = map[string]*NetworkConfig{
	// Base Mainnet (Primary network)
	BaseMainnet: {
		ChainID:         8453,
		Name:            "Base",
		RPCEndpoints:    []string{"https://mainnet.base.org", "https://base.llamarpc.com"},
		ContractAddress: common.HexToAddress("******************************************"),
		BlockTime:       2 * time.Second,
		GasLimit:        3000000,
		MaxGasPrice:     10000000000, // 10 Gwei
		IsTestnet:       false,
	},
	// Polygon Mainnet
	PolygonMainnet: {
		ChainID:         137,
		Name:            "Polygon",
		RPCEndpoints:    []string{"https://polygon-rpc.com", "https://polygon.llamarpc.com"},
		ContractAddress: common.HexToAddress("******************************************"),
		BlockTime:       2 * time.Second,
		GasLimit:        3000000,
		MaxGasPrice:     50000000000, // 50 Gwei
		IsTestnet:       false,
	},
	// BNB Smart Chain
	BSCMainnet: {
		ChainID:         56,
		Name:            "BNB Smart Chain",
		RPCEndpoints:    []string{"https://bsc-dataseed.binance.org", "https://binance.llamarpc.com"},
		ContractAddress: common.HexToAddress("******************************************"),
		BlockTime:       3 * time.Second,
		GasLimit:        3000000,
		MaxGasPrice:     10000000000, // 10 Gwei
		IsTestnet:       false,
	},
	// Lisk Mainnet
	LiskMainnet: {
		ChainID:         1135,
		Name:            "Lisk",
		RPCEndpoints:    []string{"https://rpc.api.lisk.com"},
		ContractAddress: common.HexToAddress("******************************************"),
		BlockTime:       2 * time.Second,
		GasLimit:        3000000,
		MaxGasPrice:     1000000000, // 1 Gwei
		IsTestnet:       false,
	},
	// Ethereum Mainnet (if needed)
	EthereumMainnet: {
		ChainID:         1,
		Name:            "Ethereum",
		RPCEndpoints:    []string{"https://eth.llamarpc.com", "https://ethereum.publicnode.com"},
		ContractAddress: common.Address{}, // Not deployed yet
		BlockTime:       12 * time.Second,
		GasLimit:        3000000,
		MaxGasPrice:     50000000000, // 50 Gwei
		IsTestnet:       false,
	},
}

// GetNetworkConfig returns the network configuration for a given network name
func GetNetworkConfig(networkName string) (*NetworkConfig, bool) {
	config, exists := SupportedNetworks[networkName]
	return config, exists
}

// GetNetworkConfigByChainID returns the network configuration for a given chain ID
func GetNetworkConfigByChainID(chainID uint64) (*NetworkConfig, string, bool) {
	for networkName, config := range SupportedNetworks {
		if config.ChainID == chainID {
			return config, networkName, true
		}
	}
	return nil, "", false
}

// GetSupportedNetworks returns a list of all supported network names
func GetSupportedNetworks() []string {
	networks := make([]string, 0, len(SupportedNetworks))
	for networkName := range SupportedNetworks {
		networks = append(networks, networkName)
	}
	return networks
}

// GetSupportedChainIDs returns a list of all supported chain IDs
func GetSupportedChainIDs() []uint64 {
	chainIDs := make([]uint64, 0, len(SupportedNetworks))
	for _, config := range SupportedNetworks {
		chainIDs = append(chainIDs, config.ChainID)
	}
	return chainIDs
}

// IsNetworkSupported checks if a network name is supported
func IsNetworkSupported(networkName string) bool {
	_, exists := SupportedNetworks[networkName]
	return exists
}

// IsChainSupported checks if a chain ID is supported
func IsChainSupported(chainID uint64) bool {
	for _, config := range SupportedNetworks {
		if config.ChainID == chainID {
			return true
		}
	}
	return false
}

// GetMainnetNetworks returns only mainnet networks (non-testnet)
func GetMainnetNetworks() map[string]*NetworkConfig {
	mainnets := make(map[string]*NetworkConfig)
	for networkName, config := range SupportedNetworks {
		if !config.IsTestnet {
			mainnets[networkName] = config
		}
	}
	return mainnets
}

// GetNetworkNameByChainID returns the network name for a given chain ID
func GetNetworkNameByChainID(chainID uint64) (string, bool) {
	for networkName, config := range SupportedNetworks {
		if config.ChainID == chainID {
			return networkName, true
		}
	}
	return "", false
}
