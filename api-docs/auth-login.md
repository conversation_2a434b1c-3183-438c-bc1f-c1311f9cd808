# Authentication APIs

> **Base Path:** `/api/v1/auth`
> **Content-Type:** `application/json`
> **Authentication:** None (for login), Bear<PERSON> (for protected endpoints)

These endpoints handle user authentication, token management, and session control. The enhanced authentication system supports login using either email address or username along with secure password verification.

---

## Login

> **Endpoint:** `POST /api/v1/auth/login`

Authenticates a user using either their email address or username along with their password. Returns access and refresh tokens for subsequent API calls.

### Enhanced Features

- **Flexible Authentication**: Accepts both email addresses and usernames in a single `identifier` field
- **Automatic Detection**: System automatically determines whether the identifier is an email or username
- **Security**: Bcrypt password hashing, consistent error messages to prevent user enumeration
- **Input Validation**: Comprehensive validation for both email and username formats

### Request Body

```json
{
  "identifier": "<EMAIL>",
  "password": "securepassword123"
}
```

**Alternative with Username:**
```json
{
  "identifier": "johndoe",
  "password": "securepassword123"
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `identifier` | `string` | Yes | Email address or username (3-255 characters) |
| `password` | `string` | Yes | User password (minimum 8 characters) |

### Identifier Validation Rules

#### Email Format
- Must be a valid email address (RFC 5322 compliant)
- Maximum length: 254 characters
- Case-insensitive matching

#### Username Format
- Length: 3-255 characters
- Must start and end with alphanumeric characters
- Allowed characters: letters, numbers, underscore (_), hyphen (-), dot (.)
- Cannot have consecutive special characters
- Case-insensitive matching

### Success Response

**Status Code:** `200 OK`

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 900,
  "user": {
    "id": "123",
    "email": "<EMAIL>",
    "username": "johndoe",
    "country_id": 1
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `access_token` | `string` | JWT access token for API authentication |
| `refresh_token` | `string` | JWT refresh token for obtaining new access tokens |
| `token_type` | `string` | Token type, always "Bearer" |
| `expires_in` | `integer` | Access token expiration time in seconds (900 = 15 minutes) |
| `user.id` | `string` | User's external ID |
| `user.email` | `string` | User's email address |
| `user.username` | `string` | User's username |
| `user.country_id` | `integer` | User's associated country ID |

### Failure Responses

| HTTP Status | `error` | When it happens |
|-------------|---------|-----------------|
| `400 Bad Request` | `INVALID_REQUEST` | Request payload is missing, malformed, or fails validation |
| `401 Unauthorized` | `LOGIN_FAILED` | Invalid credentials (email/username not found or incorrect password) |
| `500 Internal Server Error` | `LOGIN_FAILED` | Server error during authentication process |

### Error Response Examples

#### Invalid Request Format
```json
{
  "code": 400,
  "message": "Invalid request format",
  "error": "INVALID_REQUEST"
}
```

#### Authentication Failed
```json
{
  "code": 401,
  "message": "Login failed",
  "error": "LOGIN_FAILED"
}
```

---

## Refresh Token

> **Endpoint:** `POST /api/v1/auth/refresh`

Exchanges a valid refresh token for a new access token and refresh token pair. This implements token rotation for enhanced security.

### Request Body

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `refresh_token` | `string` | Yes | Valid refresh token from login or previous refresh |

### Success Response

**Status Code:** `200 OK`

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 900,
  "user": {
    "id": "123",
    "email": "<EMAIL>",
    "username": "johndoe",
    "country_id": 1
  }
}
```

### Token Rotation

- The old refresh token is automatically revoked
- A new refresh token is issued with the response
- Refresh tokens expire after 7 days
- Only one refresh token per user session is valid at a time

### Failure Responses

| HTTP Status | `error` | When it happens |
|-------------|---------|-----------------|
| `400 Bad Request` | `INVALID_REQUEST` | Request payload is missing or malformed |
| `401 Unauthorized` | `REFRESH_TOKEN_FAILED` | Invalid, expired, or revoked refresh token |
| `500 Internal Server Error` | `REFRESH_TOKEN_FAILED` | Server error during token refresh |

---

## Logout

> **Endpoint:** `POST /api/v1/auth/logout`

Revokes the user's refresh token, effectively logging them out from the current session.

### Request Body

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `refresh_token` | `string` | Yes | Refresh token to revoke |

### Success Response

**Status Code:** `200 OK`

```json
{
  "code": 200,
  "message": "Successfully logged out"
}
```

### Failure Responses

| HTTP Status | `error` | When it happens |
|-------------|---------|-----------------|
| `400 Bad Request` | `INVALID_REQUEST` | Request payload is missing or malformed |
| `401 Unauthorized` | `LOGOUT_FAILED` | Invalid refresh token or logout process failed |
| `500 Internal Server Error` | `LOGOUT_FAILED` | Server error during logout |

---

## Example cURL Snippets

### Login with Email
```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/auth/login' \
  --header 'Content-Type: application/json' \
  --data '{
    "identifier": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### Login with Username
```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/auth/login' \
  --header 'Content-Type: application/json' \
  --data '{
    "identifier": "johndoe",
    "password": "securepassword123"
  }'
```

### Refresh Token
```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/auth/refresh' \
  --header 'Content-Type: application/json' \
  --data '{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

### Logout
```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/auth/logout' \
  --header 'Content-Type: application/json' \
  --data '{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

---

## Security Features

### Password Security
- Passwords are hashed using bcrypt with default cost (10)
- Password verification uses constant-time comparison
- Minimum password length: 8 characters

### Token Security
- JWT tokens with HMAC-SHA256 signing
- Access tokens expire after 15 minutes
- Refresh tokens expire after 7 days
- Automatic token rotation on refresh
- Secure token storage with SHA-256 hashing

### Input Security
- Comprehensive input validation and sanitization
- SQL injection prevention through parameterized queries
- Consistent error messages prevent user enumeration
- Case-insensitive identifier matching

### Rate Limiting
- Consider implementing rate limiting at the API gateway level
- Recommended: 5 login attempts per minute per IP
- Account lockout after multiple failed attempts (implementation dependent)

---

## Authentication Flow

1. **Login**: User provides identifier (email/username) and password
2. **Validation**: System validates credentials and returns tokens
3. **API Access**: Use access token in Authorization header: `Bearer <access_token>`
4. **Token Refresh**: When access token expires, use refresh token to get new tokens
5. **Logout**: Revoke refresh token to end session

### Using Access Tokens

Include the access token in the Authorization header for protected endpoints:

```bash
curl -X GET 'https://{{BASE_URL}}/api/v1/protected-endpoint' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
```
