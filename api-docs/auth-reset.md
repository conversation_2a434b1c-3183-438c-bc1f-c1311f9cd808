# Password Reset APIs

> **Base Path:** `/api/v1/auth/reset`
> **Content-Type:** `application/json`
> **Authentication:** None

These endpoints let users request a password reset email and complete the password reset flow using the token delivered in that email. Responses follow the common application envelope with `code`, `message`, `data`, and optional `error` fields.

---

## Request Reset Token

> **Endpoint:** `POST /api/v1/auth/reset`

Triggers generation of a password reset token. If the email belongs to a registered user, a reset link is sent. For non-existent emails, the handler still returns a success envelope to avoid user enumeration.

### Request Body

```json
{
  "email": "<EMAIL>"
}
```

### Success Response

**Status Code:** `202 Accepted`

```json
{
  "code": 202,
  "message": "If the account exists, a reset email has been sent",
  "data": null
}
```

### Failure Responses

| HTTP Status | `error` | When it happens |
|-------------|---------|-----------------|
| `400 Bad Request` | `INVALID_REQUEST` | Payload is missing or fails validation (e.g., malformed email). |
| `500 Internal Server Error` | `RESET_TOKEN_CREATE_FAILED` | Downstream failure while generating the token or sending email. |

---

## Verify Reset Token

> **Endpoint:** `POST /api/v1/auth/reset/verify`

Validates a reset token and updates the user password when both the token and `reset_id` match and are still active.

### Request Body

```json
{
  "reset_id": "e9be7417-2f9f-4e52-971d-3d0d52d8e7bc",
  "token": "a2fd1b53-9c3b-4ce8-81f5-9451e8475d46",
  "password": "NewStrongP@ssword1"
}
```

### Success Response

**Status Code:** `200 OK`

```json
{
  "code": 200,
  "message": "Reset token verified",
  "data": {
    "send_to": "<EMAIL>",
    "reset_id": "e9be7417-2f9f-4e52-971d-3d0d52d8e7bc",
    "token": "a2fd1b53-9c3b-4ce8-81f5-9451e8475d46",
    "created_at": "2024-05-08T14:20:15Z",
    "expires_at": "2024-05-08T15:20:15Z",
    "used_at": "2024-05-08T14:35:02Z"
  }
}
```

> `reset_link` is only populated in the notification email context and may be `null` in API responses.

### Failure Responses

| HTTP Status | `error` | When it happens |
|-------------|---------|-----------------|
| `400 Bad Request` | `INVALID_REQUEST` | Payload fails validation. |
| `400 Bad Request` | `RESET_TOKEN_INVALID` | Token and ID do not match, token expired, or it was already used. |
| `500 Internal Server Error` | `RESET_TOKEN_VERIFY_FAILED` | Password hashing or persistence failed. |

---

## Example cURL Snippets

### Requesting a Reset

```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/auth/reset' \
  --header 'Content-Type: application/json' \
  --data '{"email": "<EMAIL>"}'
```

### Verifying a Reset Token

```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/auth/reset/verify' \
  --header 'Content-Type: application/json' \
  --data '{
    "reset_id": "e9be7417-2f9f-4e52-971d-3d0d52d8e7bc",
    "token": "a2fd1b53-9c3b-4ce8-81f5-9451e8475d46",
    "password": "NewStrongP@ssword1"
  }'
```

---

## Validation Notes

- `email` must be a valid email address per the global validator configuration.
- `reset_id` must be a UUID string matching an existing reset record.
- `token` must match the one issued for the `reset_id` and remain unused before expiry.
- `password` must observe any password-strength rules enforced by the service.
