# Health Check API

> **Endpoint:** `GET /health`
> **Content-Type:** `application/json`
> **Authentication:** None

## Overview

This endpoint provides comprehensive health status information about the application and its dependencies. It performs connectivity checks on critical services like the database and returns detailed status information including uptime and connection metrics.

---

## Parameters

This endpoint does not require any parameters.

---

## Example Request

### cURL Command

```bash
curl -X GET 'https://your-api-domain.com/health' \
  --header 'Accept: application/json'
```

### Request Payload Structure

No request body required for this endpoint.

---

## Success Response

**Status Code:** `200 OK`

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:45Z",
  "uptime": "2d 14h 23m 12s",
  "database": {
    "status": "healthy",
    "connections_open": 5,
    "connections_idle": 15
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | `string` | Overall health status: "healthy" or "unhealthy" |
| `timestamp` | `string` | Current server timestamp in ISO 8601 format |
| `uptime` | `string` | Human-readable server uptime (days, hours, minutes, seconds) |
| `database.status` | `string` | Database connectivity status: "healthy", "unhealthy", or "unknown" |
| `database.connections_open` | `integer` | Number of active database connections |
| `database.connections_idle` | `integer` | Number of idle database connections in the pool |
| `details` | `object` | Optional error details when status is "unhealthy" |

---

## Error Responses

### Server Errors (500 Internal Server Error)

When database connectivity fails or other critical services are unavailable:

```json
{
  "status": "unhealthy",
  "timestamp": "2024-01-15T10:30:45Z",
  "uptime": "2d 14h 23m 12s",
  "database": {
    "status": "unhealthy",
    "connections_open": 0,
    "connections_idle": 0
  },
  "details": {
    "database_error": "connection refused: database server unavailable"
  }
}
```

---

## Health Check Logic

The endpoint performs the following checks:

1. **Application Status** - Verifies the service is running and responding
2. **Database Connectivity** - Tests connection pool with ping operation
3. **Resource Metrics** - Gathers connection pool statistics
4. **Overall Assessment** - Determines combined health status

### Status Determination

- **healthy**: All systems operational and database connectivity confirmed
- **unhealthy**: Database connection failed or critical errors detected
- **unknown**: Initial state before checks complete
