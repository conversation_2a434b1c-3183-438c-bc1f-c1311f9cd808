# User Registration API

> **Endpoint:** `POST /api/v1/users/register`
> **Content-Type:** `application/json`
> **Authentication:** None

## Overview

This endpoint creates a new user account with email, username, and password. The system enforces unique constraints on both email and username, and implements secure password hashing using bcrypt.

---

## Request Body

```json
{
  "email": "<EMAIL>",
  "username": "joh<PERSON><PERSON>",
  "password": "securepassword123",
  "country_id": 1
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `email` | `string` | Yes | Valid email address (unique across all users) |
| `username` | `string` | Yes | Username (3-255 characters, unique across all users) |
| `password` | `string` | Yes | Password (8-255 characters) |
| `country_id` | `integer` | Yes | Valid country ID from the countries table |

### Validation Rules

#### Email Validation
- Must be a valid email address format
- Must be unique across all users
- Case-insensitive uniqueness check
- Maximum length: 254 characters (RFC 5321 limit)

#### Username Validation
- Length: 3-255 characters
- Must be unique across all users
- Must start and end with alphanumeric characters
- Allowed characters: letters, numbers, underscore (_), hyphen (-), dot (.)
- Cannot have consecutive special characters
- Case-insensitive uniqueness check

#### Password Validation
- Minimum length: 8 characters
- Maximum length: 255 characters
- Automatically hashed using bcrypt with default cost (10)
- No additional complexity requirements enforced at API level

#### Country ID Validation
- Must be a valid integer
- Must reference an existing country in the countries table
- Required field for user registration

---

## Success Response

**Status Code:** `201 Created`

```json
{
  "code": 201,
  "message": "User registered successfully",
  "data": {
    "id": "01234567-89ab-cdef-0123-456789abcdef",
    "email": "<EMAIL>",
    "username": "johndoe",
    "country_id": 1,
    "created_at": "2024-01-15T10:30:45Z",
    "updated_at": "2024-01-15T10:30:45Z"
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `code` | `integer` | HTTP status code (201) |
| `message` | `string` | Success message |
| `data.id` | `string` | User's external UUID v7 identifier |
| `data.email` | `string` | User's email address |
| `data.username` | `string` | User's username |
| `data.country_id` | `integer` | User's associated country ID |
| `data.created_at` | `string` | Account creation timestamp (ISO 8601) |
| `data.updated_at` | `string` | Last update timestamp (ISO 8601) |

---

## Error Responses

### Validation Errors (400 Bad Request)

#### Invalid Request Format
```json
{
  "code": 400,
  "message": "Invalid request format",
  "error": "Failed to parse request body"
}
```

#### Validation Failed
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": "Key: 'UserRequest.Email' Error:Tag: 'email' Key: 'UserRequest.Password' Error:Tag: 'min'"
}
```

### Conflict Errors (409 Conflict)

#### Email Already Exists
```json
{
  "code": 409,
  "message": "Account already exists",
  "error": "A user with this email already exists"
}
```

### Server Errors (500 Internal Server Error)

#### Registration Failed
```json
{
  "code": 500,
  "message": "Failed to register user",
  "error": "Database connection error"
}
```

---

## Example cURL Command

```bash
curl -X POST 'https://{{BASE_URL}}/api/v1/users/register' \
  --header 'Content-Type: application/json' \
  --data '{
    "email": "<EMAIL>",
    "username": "johndoe",
    "password": "securepassword123",
    "country_id": 1
  }'
```

---

## Registration Flow

1. **Input Validation**: System validates all required fields and formats
2. **Uniqueness Check**: Verifies email and username are not already taken
3. **Password Hashing**: Securely hashes the password using bcrypt
4. **UUID Generation**: Creates a UUIDv7 for the external identifier
5. **Database Storage**: Saves the user record with all required fields
6. **Response**: Returns the created user information (excluding password)

---

## Security Features

### Password Security
- Passwords are hashed using bcrypt with default cost (10)
- Original passwords are never stored or logged
- Password hashes are irreversible

### Data Protection
- Email addresses are stored in lowercase for consistency
- Usernames are stored in lowercase for consistency
- Sensitive data is excluded from API responses

### Input Sanitization
- All inputs are validated and sanitized
- SQL injection prevention through parameterized queries
- XSS prevention through proper input handling

---

## Database Schema

The registration creates a record in the `users` table with the following structure:

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    external_id UUID NOT NULL UNIQUE,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    status VARCHAR(25),
    country_id INTEGER REFERENCES countries(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

### Default Values
- `first_name` and `last_name`: Empty strings (TODO: Add to registration form)
- `status`: "non-active" (TODO: Implement activation flow)
- `phone_number`: NULL (optional field)
- `external_id`: Auto-generated UUIDv7
- `created_at` and `updated_at`: Current timestamp

---

## Next Steps After Registration

1. **Account Activation**: Implement email verification flow (if required)
2. **Login**: Use the [Authentication API](auth-login.md) to log in with email/username and password
3. **Profile Completion**: Add first name, last name, and phone number through profile update endpoints

---

## Related Endpoints

- [Login with Email/Username](auth-login.md) - Authenticate after registration
- [Password Reset](auth-reset.md) - Reset forgotten passwords
- [Health Check](get-health.md) - Verify API availability

---

## Validation Error Examples

### Missing Required Fields
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": "Key: 'UserRequest.Email' Error:Tag: 'required'"
}
```

### Invalid Email Format
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": "Key: 'UserRequest.Email' Error:Tag: 'email'"
}
```

### Username Too Short
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": "Key: 'UserRequest.Username' Error:Tag: 'min'"
}
```

### Password Too Short
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": "Key: 'UserRequest.Password' Error:Tag: 'min'"
}
```

### Invalid Country ID
```json
{
  "code": 400,
  "message": "Validation failed",
  "error": "Key: 'UserRequest.CountryID' Error:Tag: 'required'"
}
```
