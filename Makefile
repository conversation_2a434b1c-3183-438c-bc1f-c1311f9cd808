.PHONY: help build run watch migrate migrate-up migrate-down migrate-status migrate-reset migrate-version migrate-create clean deps test fmt lint
.PHONY: tools tidy setup-hooks build-migration docker-build test-coverage test-race test-bench security pre-commit quality
.PHONY: generate-contracts

# Show this help message
help: ## Show this help message
	@grep -E -h '\s##\s' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Install all development tools via mise
tools: ## Install all development tools via mise
	@echo "Installing tools via mise..."
	mise install

# Clean up go.mod and go.sum files
tidy: ## Clean up go.mod and go.sum files
	go mod tidy

# Install pre-commit hooks
setup-hooks: ## Install pre-commit hooks
	@echo "Setting up pre-commit hooks..."
	pre-commit install --install-hooks

# Build the application
build: ## Compile the REST API binary
	@echo "Compiling REST API server..."
	go build -o bin/telescope main.go

# Build Docker image with all required build args
docker-build: ## Build Docker image with all required build args
	@echo "Building Docker image..."
	docker build \
		--build-arg BUILD_VERSION=$$(git describe --tags --always --dirty 2>/dev/null || echo "dev") \
		--build-arg BUILD_DATE=$$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
		--build-arg VCS_REF=$$(git rev-parse HEAD 2>/dev/null || echo "unknown") \
		-t telescope-backend:latest \
		-f deployment/Dockerfile \
		.

# Run the server
run: ## Start the API server
	@echo "Starting REST API server..."
	go run main.go server

# Run with air (hot reload)
watch: ## Run the application with hot reload using Air
	air -c scripts/air/backend.toml

# Run migrations
migrate: migrate-up ## Run migrations

# Run migrations up
migrate-up: ## Run migrations up
	@go run main.go migrate up

# Run migrations down
migrate-down: ## Run migrations down
	@go run main.go migrate down

# Check migration status
migrate-status: ## Check migration status
	@go run main.go migrate status

# Reset all migrations
migrate-reset: ## Reset all migrations
	@go run main.go migrate reset

# Show current migration version
migrate-version: ## Show current migration version
	@go run main.go migrate version

# Create new migration file
migrate-create: ## Create new migration file (usage: make migrate-create NAME=migration_name)
	@if [ -z "$(NAME)" ]; then \
		echo "Error: NAME is required. Usage: make migrate-create NAME=migration_name"; \
		exit 1; \
	fi
	@go run main.go migrate create $(NAME)

# Show app help
app-help: ## Show application help
	@go run main.go --help

# Show server help
server-help: ## Show server help
	@go run main.go server --help

# Show migration help
migrate-help: ## Show migration help
	go run main.go migrate --help

# Clean build artifacts and temporary files
clean: ## Remove build artifacts and temporary files
	@echo "Cleaning build artifacts..."
	rm -rf bin/
	rm -f coverage.out coverage.html

# Install dependencies
deps: ## Install dependencies
	go mod download
	go mod tidy

# Run tests
test: ## Run all tests
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage report
test-coverage: ## Run tests with coverage report
	@echo "Running tests with coverage..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Run tests with race condition detection
test-race: ## Run tests with race condition detection
	@echo "Running tests with race detection..."
	go test -v -race ./...

# Run benchmark tests
test-bench: ## Run benchmark tests with memory profiling
	@echo "Running benchmark tests..."
	go test -v -bench=. -benchmem ./...

# Format code using gofumpt and goimports-reviser
fmt: tidy ## Format Go code using gofumpt and goimports-reviser
	@echo "Formatting code..."
	@find . -type f -name '*.go' \
		-not -path './vendor/*' \
		-not -path './scripts/*' \
		-not -path './tools/*' \
		-not -path './bin/*' \
		| xargs gofumpt -w
	@find . -type f -name '*.go' \
		-not -path './vendor/*' \
		-not -path './scripts/*' \
		-not -path './tools/*' \
		-not -path './bin/*' \
		| xargs goimports-reviser -rm-unused -set-alias -format -output file

# Lint code
lint: ## Run golangci-lint on the codebase
	@echo "Linting code..."
	@golangci-lint run -v

# Run security scan using gosec
security: ## Run security scan using gosec
	@echo "Running security scan..."
	gosec ./...

# Run pre-commit hooks on all files
pre-commit: ## Run pre-commit hooks on all files
	@echo "Running pre-commit on all files..."
	pre-commit run --all-files

# Run complete code quality checks
quality: fmt lint test-coverage security ## Run complete code quality checks
	@echo "Quality check completed!"

.PHONY: dev-setup
dev-setup: tools setup-hooks ## Set up development environment
	@echo "Development environment setup completed!"

# Generate smart contract Go bindings from ABIs
generate-contracts: ## Generate Go bindings for IDRX smart contracts
	@echo "Generating smart contract bindings..."
	@mkdir -p pkg/idrx/contracts
	abigen --abi .claude/src/idrx/abis/idrx.json --pkg contracts --type IDRX --out pkg/idrx/contracts/idrx.go --alias _totalSupply=InternalTotalSupply
	abigen --abi .claude/src/idrx/abis/proxy.json --pkg contracts --type ERC1967Proxy --out pkg/idrx/contracts/proxy.go
	@echo "Contract bindings generated successfully!"
