// Package cmd provides command implementations for the Telescope Backend CLI.
package cmd

import (
	"log"

	echo "github.com/labstack/echo/v4"
	"go.uber.org/fx"
	"go.uber.org/zap"

	"telescope-be/internal/appctx"
	appfx "telescope-be/internal/fx"
	"telescope-be/internal/server"
)

// RunServer starts the HTTP server with the given configuration
func RunServer(config *appctx.Config) error {
	if config == nil {
		// Load configuration if not provided
		var err error
		config, err = appctx.LoadConfig()
		if err != nil {
			return err
		}
	}

	// Create FX app with all dependencies
	app := fx.New(
		appfx.Module,
		fx.Supply(config.Server), // Supply the server config
		fx.Invoke(func(srv server.Server, _ *echo.Echo, _ *zap.Logger) {
			log.Printf("Starting HTTP server on %s:%s", config.Server.Host, config.Server.Port)
			if err := srv.Start(); err != nil {
				log.Fatalf("Server error: %v", err)
			}
		}),
	)

	// Start the application
	app.Run()

	return nil
}
