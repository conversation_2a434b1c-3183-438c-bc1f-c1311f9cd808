// Package cmd provides command implementations for the Telescope Backend CLI.
package cmd

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"

	_ "github.com/jackc/pgx/v5/stdlib" // PostgreSQL driver for migrations
	goose "github.com/pressly/goose/v3"

	"telescope-be/internal/appctx"
	"telescope-be/internal/constants"
)

const (
	driver              = "pgx"
	defaultMigrationDir = "./migrations"
)

// RunMigration executes a migration command with the given arguments
func RunMigration(args []string, config *appctx.Config) error {
	if config == nil {
		// Load configuration if not provided
		var err error
		config, err = appctx.LoadConfig()
		if err != nil {
			return err
		}
	}

	if len(args) < 1 {
		printMigrationUsage()
		return fmt.Errorf("migration command required")
	}

	command := args[0]

	// Use DSN from configuration
	dsn := config.Database.DSN
	if dsn == "" {
		return fmt.Errorf("database DSN is required for migrations")
	}

	// Set goose database dialect
	if err := goose.SetDialect(driver); err != nil {
		return fmt.Errorf("failed to set dialect: %w", err)
	}

	// Open database connection using pgx driver
	db, err := goose.OpenDBWithDriver(driver, dsn)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			log.Printf("Failed to close database: %v", err)
		}
	}()

	ctx := context.Background()

	if err := executeCommand(ctx, db, command, args, defaultMigrationDir); err != nil {
		return err
	}

	log.Printf("Migration command '%s' completed successfully", command)
	return nil
}

func executeCommand(ctx context.Context, db *sql.DB, command string, args []string, dir string) error {
	switch command {
	case "up":
		return goose.UpContext(ctx, db, dir)
	case "down":
		return goose.DownContext(ctx, db, dir)
	case "status":
		return goose.StatusContext(ctx, db, dir)
	case "version":
		version, err := goose.GetDBVersionContext(ctx, db)
		if err != nil {
			return fmt.Errorf("goose version: %w", err)
		}
		fmt.Printf("goose: version %d\n", version)
		return nil
	case "create":
		if len(args) < 2 {
			return fmt.Errorf("goose create: migration name required")
		}
		return goose.Create(db, dir, args[1], "sql")
	case "reset":
		return goose.ResetContext(ctx, db, dir)
	default:
		return fmt.Errorf("unknown command: %s", command)
	}
}

func printMigrationUsage() {
	dbStrName := fmt.Sprintf("%s_POSTGRES_DSN", strings.ToUpper(constants.AppName))
	fmt.Fprintf(os.Stderr, `Migration Commands:
  up             Migrate to most recent version
  down           Roll back version by 1
  status         Show migration status
  version        Print current database version
  create NAME    Create new migration file
  reset          Reset all migrations

Environment Variables:
  %s   PostgreSQL connection string (optional, falls back to config)

Examples:
  telescope migrate up
  telescope migrate down
  telescope migrate status
  telescope migrate create add_user_preferences
  telescope migrate reset
`, dbStrName)
}
