// Package cmd provides command implementations for the Telescope Backend CLI.
package cmd

import (
	"fmt"
	"os"

	"telescope-be/internal/appctx"
)

func CreateConfig() error {
	err := appctx.CreateExampleConfig()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error creating default config file: %s\n", err)
		return err
	}

	fmt.Fprintln(os.Stderr, "Success generating default config file")
	return nil
}
