repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v6.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: detect-private-key

  - repo: https://github.com/TekWizely/pre-commit-golang
    rev: v1.0.0-rc.2
    hooks:
      - id: go-fmt
      - id: go-vet-mod
      - id: go-imports
      - id: go-mod-tidy
      - id: go-test-mod

  - repo: local
    hooks:
      - id: golangci-lint
        name: golangci-lint
        entry: make lint
        language: system
        types: [go]
        pass_filenames: false

  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args:
          [
            "--exclude-files",
            "'*test.go'",
            "--exclude-files",
            "'*.md'",
            "--baseline",
            ".secrets.baseline",
          ]
        exclude: ".secrets.baseline"

  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.28.0
    hooks:
      - id: gitleaks
        args: ["--no-banner"]
        exclude: ".secrets.baseline"
