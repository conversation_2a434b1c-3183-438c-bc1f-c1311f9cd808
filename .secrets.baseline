{"version": "1.5.0", "plugins_used": [{"name": "ArtifactoryDetector"}, {"name": "AWSKeyDetector"}, {"name": "AzureStorageKeyDetector"}, {"name": "Base64HighEntropyString", "limit": 4.5}, {"name": "BasicAuthDetector"}, {"name": "CloudantDetector"}, {"name": "DiscordBotTokenDetector"}, {"name": "GitHubTokenDetector"}, {"name": "GitLabTokenDetector"}, {"name": "HexHighEntropyString", "limit": 3.0}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "IPPublicDetector"}, {"name": "JwtTokenDetector"}, {"name": "KeywordDetector", "keyword_exclude": ""}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "OpenAIDetector"}, {"name": "PrivateKeyDetector"}, {"name": "PypiTokenDetector"}, {"name": "SendGridDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TelegramBotTokenDetector"}, {"name": "TwilioKeyDetector"}], "filters_used": [{"path": "detect_secrets.filters.allowlist.is_line_allowlisted"}, {"path": "detect_secrets.filters.common.is_baseline_file", "filename": ".secrets.baseline"}, {"path": "detect_secrets.filters.common.is_ignored_due_to_verification_policies", "min_level": 2}, {"path": "detect_secrets.filters.heuristic.is_indirect_reference"}, {"path": "detect_secrets.filters.heuristic.is_likely_id_string"}, {"path": "detect_secrets.filters.heuristic.is_lock_file"}, {"path": "detect_secrets.filters.heuristic.is_not_alphanumeric_string"}, {"path": "detect_secrets.filters.heuristic.is_potential_uuid"}, {"path": "detect_secrets.filters.heuristic.is_prefixed_with_dollar_sign"}, {"path": "detect_secrets.filters.heuristic.is_sequential_string"}, {"path": "detect_secrets.filters.heuristic.is_swagger_file"}, {"path": "detect_secrets.filters.heuristic.is_templated_secret"}, {"path": "detect_secrets.filters.regex.should_exclude_file", "pattern": ["'*test.go'", "'*.md'"]}], "results": {".dist.env": [{"type": "Basic Auth Credentials", "filename": ".dist.env", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 11}], "README.md": [{"type": "Secret Keyword", "filename": "README.md", "hashed_secret": "564e340cd48437d2dfe876ee154cc99dc4d0d137", "is_verified": false, "line_number": 92}], "configs/example.config.yaml": [{"type": "Secret Keyword", "filename": "configs/example.config.yaml", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 13}], "documentation/ARCHITECTURE.md": [{"type": "Secret Keyword", "filename": "documentation/ARCHITECTURE.md", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 173}], "documentation/TECHNICAL_SPECIFICATION.md": [{"type": "Secret Keyword", "filename": "documentation/TECHNICAL_SPECIFICATION.md", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 346}], "internal/appctx/config.go": [{"type": "Basic Auth Credentials", "filename": "internal/appctx/config.go", "hashed_secret": "5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8", "is_verified": false, "line_number": 28}], "pkg/idrx/BLOCKCHAIN.md": [{"type": "Secret Keyword", "filename": "pkg/idrx/BLOCKCHAIN.md", "hashed_secret": "7f96233f6539ba514b60758637dfb2dd2244eba7", "is_verified": false, "line_number": 179}, {"type": "Secret Keyword", "filename": "pkg/idrx/BLOCKCHAIN.md", "hashed_secret": "11fa7c37d697f30e6aee828b4426a10f83ab2380", "is_verified": false, "line_number": 182}, {"type": "Secret Keyword", "filename": "pkg/idrx/BLOCKCHAIN.md", "hashed_secret": "33f220dd67f717cc949db63e21c90e130a6137da", "is_verified": false, "line_number": 183}], "pkg/idrx/auth_test.go": [{"type": "Secret Keyword", "filename": "pkg/idrx/auth_test.go", "hashed_secret": "cc76f8f5c5864f4f4d68aead0c4c8f07911dca1f", "is_verified": false, "line_number": 19}, {"type": "Secret Keyword", "filename": "pkg/idrx/auth_test.go", "hashed_secret": "fbc6c7d85067efe1a3dcf092a0999044c14b5fa5", "is_verified": false, "line_number": 29}, {"type": "Secret Keyword", "filename": "pkg/idrx/auth_test.go", "hashed_secret": "03f38ca8d2e9cf44fa8b2d0a4c5f4556c944cfdf", "is_verified": false, "line_number": 33}, {"type": "Secret Keyword", "filename": "pkg/idrx/auth_test.go", "hashed_secret": "053286d068e59f77ce97ef25284e594c2ce88e9e", "is_verified": false, "line_number": 43}], "pkg/idrx/signature_test.go": [{"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "643549afe4b11733ab9ba44a1dc6ecad0c145d2f", "is_verified": false, "line_number": 67}, {"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "7f162371a30748392cb2f9d1257f9696d80d3f65", "is_verified": false, "line_number": 68}, {"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "2cff19b62da1ac6f1758b5dabcaa2d32106e9607", "is_verified": false, "line_number": 78}, {"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "1cb6b41328e76eb4613f5d1a831140d9a20bfa54", "is_verified": false, "line_number": 95}, {"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "d59ebea0caf7329c6cfbed53bca592f7089d5d03", "is_verified": false, "line_number": 110}, {"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "d391d081b08f815a0ed9245c84408b3d66319977", "is_verified": false, "line_number": 120}, {"type": "Base64 High Entropy String", "filename": "pkg/idrx/signature_test.go", "hashed_secret": "6dad4465316babb3ad5f16670a17e7095010cd2c", "is_verified": false, "line_number": 130}], "scripts/envs/env.postgres": [{"type": "Secret Keyword", "filename": "scripts/envs/env.postgres", "hashed_secret": "4390bda6cbfbf619787b1186e5c8cf019488b79d", "is_verified": false, "line_number": 2}]}, "generated_at": "2025-09-25T05:31:01Z"}