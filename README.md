# Telescope Backend

A Go-based HTTP server with graceful shutdown, database migrations using Goose, and proper layered architecture. Built with Echo framework, Cobra CLI, and Viper configuration management.

## Features

- **Clean Architecture**: Separation of concerns with distinct layers (Handler → Service → Repository)
- **HTTP Server**: High-performance server built with Echo framework
- **Database Migrations**: Version-controlled schema management using Goose
- **Configuration Management**: YAML-based configuration with environment variable support
- **Graceful Shutdown**: Proper signal handling and resource cleanup
- **CORS Support**: Configurable cross-origin resource sharing
- **CLI Interface**: Unified command-line interface using Cobra
- **Middleware**: Built-in logging, recovery, and CORS middleware

## Project Structure

```
telescope-be/
├── cmd/                    # Application entry points and commands
│   ├── migration.go       # Database migration commands
│   └── server.go          # HTTP server commands
├── configs/               # Configuration files
│   └── config.yaml        # Main configuration file
├── deployment/            # Deployment configurations
├── documentation/         # Project documentation
├── internal/              # Private application code
│   ├── appctx/           # Application context and configuration
│   ├── constants/        # Application constants
│   ├── entity/           # Domain entities
│   ├── handler/          # HTTP request handlers
│   ├── middleware/       # HTTP middleware
│   ├── presentation/     # Request/Response DTOs
│   ├── repository/       # Data access layer
│   ├── routes/           # HTTP routing configuration
│   ├── server/           # HTTP server implementation
│   └── service/          # Business logic layer
├── migrations/            # Database migration files
├── pkg/                  # Public packages (if any)
├── go.mod                # Go module definition
├── go.sum                # Go module checksums
├── main.go               # Application entry point
├── Makefile              # Build and development commands
├── ARCHITECTURE.md       # Comprehensive architecture documentation
├── TECHNICAL_SPECIFICATION.md # Detailed technical implementation guide
└── README.md             # This file
```

## Prerequisites

- **Go**: 1.24 or higher
- **Database**: PostgreSQL (configured in config.yaml)
- **Make**: Optional, for using Makefile commands

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd telescope-be
```

### 2. Install Dependencies

```bash
make deps
# or manually:
go mod download
go mod tidy
```

### 3. Configure the Application

Copy and modify the configuration file:

```bash
cp configs/config.yaml configs/config.local.yaml
# Edit configs/config.local.yaml with your database settings
```

### 4. Set Up Database

Create a PostgreSQL database and update the configuration:

```yaml
database:
  driver: "postgres"
  host: "localhost"
  port: "5432"
  user: "postgres"
  password: "your_password"
  database: "telescope"
```

### 5. Run Migrations

```bash
make migrate-up
# or
go run main.go migrate up
```

### 6. Start the Server

```bash
make run
# or
go run main.go server
```

The server will start on `http://localhost:8080`

## Usage

### CLI Commands

The application provides a unified CLI interface:

```bash
# Show all available commands
go run main.go --help

# Start the HTTP server
go run main.go server

# Database migrations
go run main.go migrate up      # Apply migrations
go run main.go migrate down    # Rollback last migration
go run main.go migrate status  # Check migration status
go run main.go migrate reset   # Reset all migrations

# Show help for specific commands
go run main.go server --help
go run main.go migrate --help
```

### Makefile Commands

```bash
# Development
make run              # Start development server
make build            # Build binary
make test             # Run tests
make fmt              # Format code
make lint             # Lint code
make clean            # Clean build artifacts

# Database
make migrate          # Run migrations up
make migrate-up       # Apply migrations
make migrate-down     # Rollback migrations
make migrate-status   # Check status
make migrate-reset    # Reset all migrations

# Help
make help             # Show all commands
make server-help      # Server command help
make migrate-help     # Migration command help
```

### Configuration

The application supports multiple configuration sources with the following priority:

1. **Command-line flags**: `--config /path/to/config.yaml`
2. **Environment variables**: Automatically loaded by Viper
3. **Configuration files**: YAML files in multiple locations
4. **Default values**: Sensible defaults for all options

#### Environment Variables

Override configuration using environment variables:

```bash
export CONFIG_PATH=/etc/telescope/config.yaml
export SERVER_PORT=9090
export DATABASE_HOST=postgres.production
export DATABASE_PASSWORD=secure_password
export LOGGER_LEVEL=info
```

#### Configuration File Locations

The application searches for configuration files in this order:

1. `./config.yaml`
2. `./configs/config.yaml`
3. `./internal/config/config.yaml`

## API Endpoints

### Duck API

The application provides a sample Duck API to demonstrate the architecture:

```
GET    /api/v1/duck      - Retrieve duck information
POST   /api/v1/duck      - Create a new duck
```

#### Example Usage

```bash
# Get duck information
curl http://localhost:8080/api/v1/duck

# Create a new duck
curl -X POST http://localhost:8080/api/v1/duck \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Donald",
    "breed": "Mallard",
    "sound": "Quack"
  }'
```

#### API Contract

```go
// Request
type DuckCreateRequest struct {
    Name  string `json:"name" validate:"required,min=1,max=100"`
    Breed string `json:"breed" validate:"required,min=1,max=50"`
    Sound string `json:"sound" validate:"required,min=1,max=50"`
}

// Response
type DuckCreateResponse struct {
    Name  string `json:"name"`
    Breed string `json:"breed"`
    Sound string `json:"sound"`
}
```

## Architecture

Telescope Backend follows Clean Architecture principles with clear separation of concerns:

### Layers

1. **Presentation Layer** (`internal/handler/`): HTTP request/response handling
2. **Service Layer** (`internal/service/`): Business logic and orchestration
3. **Repository Layer** (`internal/repository/`): Data access and persistence
4. **Entity Layer** (`internal/entity/`): Domain models and business rules
5. **Presentation DTOs** (`internal/presentation/`): API contracts and validation

### Data Flow

```
HTTP Request → Echo Router → Middleware → Handler → Service → Repository → Database
                ↓
HTTP Response ← Echo Router ← Middleware ← Handler ← Service ← Repository ← Database
```

### Key Components

- **Echo Framework**: High-performance HTTP server
- **Cobra**: CLI command structure
- **Viper**: Configuration management
- **Goose**: Database migrations
- **Clean Architecture**: Separation of concerns

## Development

### Code Quality

```bash
# Format code
make fmt

# Lint code
make lint

# Run tests
make test

# Check test coverage
go test -cover ./...
```

### Project Structure Guidelines

- **`internal/`**: Private application code
- **`cmd/`**: Application entry points
- **`configs/`**: Configuration files
- **`migrations/`**: Database schema changes
- **`pkg/`**: Public packages (if any)

### Adding New Features

1. **Entity**: Define domain model in `internal/entity/`
2. **DTOs**: Create request/response structs in `internal/presentation/`
3. **Repository**: Implement data access in `internal/repository/`
4. **Service**: Add business logic in `internal/service/`
5. **Handler**: Create HTTP handlers in `internal/handler/`
6. **Routes**: Register endpoints in `internal/routes/`

## Database

### Migrations

Database schema changes are managed using Goose:

```bash
# Create new migration
goose create add_users_table sql

# Apply migrations
make migrate-up

# Check status
make migrate-status

# Rollback
make migrate-down
```

### Migration Files

Migrations are stored in `migrations/` with the format:

```sql
-- +goose Up
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- +goose Down
DROP TABLE users;
```

## Deployment

### Building

```bash
# Build for current platform
make build

# Build for specific platform
GOOS=linux GOARCH=amd64 go build -o bin/telescope main.go
```

### Docker

```dockerfile
FROM golang:1.24-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs
EXPOSE 8080
CMD ["./main", "server"]
```

### Environment Configuration

```bash
# Production
export CONFIG_PATH=/etc/telescope/config.yaml
export SERVER_PORT=8080
export DATABASE_HOST=postgres.production
export LOGGER_LEVEL=info
```

## Monitoring and Observability

### Health Check

```bash
# Health check endpoint (to be implemented)
curl http://localhost:8080/health
```

### Logging

The application provides structured logging with configurable levels:

```yaml
logger:
  level: "info"    # debug, info, warn, error
  format: "json"   # json, text
```

### Metrics

Performance metrics and monitoring endpoints (future enhancement).

## Security

### Current Features

- CORS configuration
- Input validation
- Graceful error handling
- Secure headers (future enhancement)

### Planned Enhancements

- JWT authentication
- Rate limiting
- API key management
- Role-based access control

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Ensure code quality (`make fmt`, `make lint`, `make test`)
6. Submit a pull request

## Documentation

- **`ARCHITECTURE.md`**: Comprehensive architecture documentation
- **`TECHNICAL_SPECIFICATION.md`**: Detailed implementation guide
- **`README.md`**: This quick start guide

## License

[Add your license information here]

## Support

For questions or issues, please refer to the project documentation or create an issue in the repository.
