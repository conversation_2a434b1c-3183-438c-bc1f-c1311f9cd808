// Package main provides the entry point for the Telescope Backend application.
package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	commands "telescope-be/cmd"
	"telescope-be/internal/appctx"
)

var config *appctx.Config

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "telescope",
	Short: "Telescope Backend - A Go-based HTTP server with database migrations",
	Long: `Telescope Backend is a Go-based HTTP server with graceful shutdown,
database migrations using Goose, and proper layered architecture.

Features:
- HTTP server with graceful shutdown
- Database migrations using Goose
- Proper layered architecture (Handler -> Service -> Repository)
- Echo framework for HTTP routing
- Configuration management with YAML

Available Commands:
	server    Start the HTTP server
	migrate   Run database migrations
	config    Create a default config file`,
}

// serverCmd represents the server command
var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "Start the HTTP server",
	Long: `Start the HTTP server with graceful shutdown support.
The server will listen on the configured host and port.`,
	RunE: func(_ *cobra.Command, _ []string) error {
		return commands.RunServer(config)
	},
}

// migrateCmd represents the migration command
var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "Run database migrations",
	Long: `Run database migrations using Goose.
Supports up, down, status, and other migration commands.`,
	RunE: func(_ *cobra.Command, args []string) error {
		return commands.RunMigration(args, config)
	},
}

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Create a default config file",
	Long:  `Create a default config file in the current directory.`,
	RunE: func(_ *cobra.Command, _ []string) error {
		return commands.CreateConfig()
	},
}

func setupCLI() {
	cobra.OnInitialize(initConfig)

	// Add subcommands
	rootCmd.AddCommand(serverCmd)
	rootCmd.AddCommand(migrateCmd)
	rootCmd.AddCommand(configCmd)
}

// initConfig reads in config file and ENV variables if set
func initConfig() {
	// Load configuration into our appctx.Config struct
	var err error
	config, err = appctx.LoadConfig()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error loading config: %v\n", err)
		os.Exit(1)
	}
}

func main() {
	// Setup CLI configuration
	setupCLI()

	// Execute command
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
